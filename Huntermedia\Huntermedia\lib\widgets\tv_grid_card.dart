import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../models/channel.dart';

class TVGridCard extends StatefulWidget {
  final Channel channel;
  final VoidCallback onTap;
  final bool autofocus;

  const TVGridCard({
    super.key,
    required this.channel,
    required this.onTap,
    this.autofocus = false,
  });

  @override
  State<TVGridCard> createState() => _TVGridCardState();
}

class _TVGridCardState extends State<TVGridCard> {
  bool _isFocused = false;
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(() {
      setState(() {
        _isFocused = _focusNode.hasFocus;
      });
    });
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Focus(
      focusNode: _focusNode,
      autofocus: widget.autofocus,
      onKeyEvent: (node, event) {
        if (event is KeyDownEvent) {
          if (event.logicalKey == LogicalKeyboardKey.select ||
              event.logicalKey == LogicalKeyboardKey.enter ||
              event.logicalKey == LogicalKeyboardKey.space) {
            widget.onTap();
            return KeyEventResult.handled;
          }
        }
        return KeyEventResult.ignored;
      },
      child: GestureDetector(
        onTap: widget.onTap,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            border:
                _isFocused
                    ? Border.all(
                      color: Theme.of(context).primaryColor,
                      width: 3,
                    )
                    : null,
            boxShadow:
                _isFocused
                    ? [
                      BoxShadow(
                        color: Theme.of(
                          context,
                        ).primaryColor.withValues(alpha: 0.4),
                        blurRadius: 12,
                        spreadRadius: 4,
                      ),
                    ]
                    : [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
          ),
          child: Card(
            elevation: _isFocused ? 12 : 4,
            margin: EdgeInsets.zero,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                gradient:
                    _isFocused
                        ? LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Theme.of(
                              context,
                            ).primaryColor.withValues(alpha: 0.1),
                            Theme.of(
                              context,
                            ).primaryColor.withValues(alpha: 0.05),
                          ],
                        )
                        : null,
              ),
              child: Column(
                children: [
                  // Channel Logo Section
                  Expanded(
                    flex: 2, // تقليل المساحة لتجنب الفيض
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          color: Colors.grey[100],
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child:
                              widget.channel.logo != null &&
                                      widget.channel.logo!.isNotEmpty
                                  ? CachedNetworkImage(
                                    imageUrl: widget.channel.logo!,
                                    fit: BoxFit.contain, // تغيير إلى contain
                                    width: double.infinity,
                                    height: double.infinity,
                                    placeholder:
                                        (context, url) => Container(
                                          color: Colors.grey[100],
                                          child: const Center(
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                            ),
                                          ),
                                        ),
                                    errorWidget:
                                        (context, url, error) => Container(
                                          color: Colors.grey[100],
                                          child: const Center(
                                            child: Icon(
                                              Icons.tv,
                                              color: Colors.grey,
                                              size: 40,
                                            ),
                                          ),
                                        ),
                                  )
                                  : Container(
                                    color: Colors.grey[100],
                                    child: const Center(
                                      child: Icon(
                                        Icons.tv,
                                        color: Colors.grey,
                                        size: 40,
                                      ),
                                    ),
                                  ),
                        ),
                      ),
                    ),
                  ),

                  // Channel Info Section
                  Expanded(
                    flex: 1, // تقليل المساحة لتجنب الفيض
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Channel Name
                          Text(
                            widget.channel.name,
                            style: TextStyle(
                              fontSize: _isFocused ? 16 : 14,
                              fontWeight: FontWeight.bold,
                              color:
                                  _isFocused
                                      ? Theme.of(context).primaryColor
                                      : Colors.black87,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.center,
                          ),

                          const SizedBox(height: 4),

                          // Channel Category
                          if (widget.channel.groupTitle != null &&
                              widget.channel.groupTitle!.isNotEmpty)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color:
                                    _isFocused
                                        ? Theme.of(
                                          context,
                                        ).primaryColor.withValues(alpha: 0.2)
                                        : Theme.of(
                                          context,
                                        ).primaryColor.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Text(
                                widget.channel.groupTitle!,
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Theme.of(context).primaryColor,
                                  fontWeight: FontWeight.w500,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
