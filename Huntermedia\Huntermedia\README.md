# Hunter TV - تطبيق القنوات التلفزيونية

تطبيق Android لعرض القنوات التلفزيونية عبر الإنترنت باستخدام ملف M3U مع دعم المشغل الداخلي والخارجي.

## 🌟 المميزات

### 📺 دعم صيغ متعددة
- **HLS (HTTP Live Streaming)** - m3u8
- **Transport Stream** - ts
- **MP4** - ملفات الفيديو العادية
- **صيغ البث المباشر الأخرى**

### 🎮 خيارات التشغيل
- **مشغل داخلي**: مدمج في التطبيق مع دعم كامل لجميع الصيغ
- **مشغل خارجي**: VLC، MX Player، وتطبيقات أخرى

### 🗂️ تنظيم القنوات
- تقسيم تلقائي حسب التصنيفات (Group Titles)
- أقسام مثل: أفلام، رياضة، أطفال، أخبار، دينية، قنوات عربية، قنوات أجنبية
- عرض عدد القنوات في كل قسم

### 🔍 البحث والتصفح
- بحث سريع في أسماء القنوات
- بحث في التصنيفات
- واجهة سهلة الاستخدام

### 💾 التخزين المحلي
- تخزين مؤقت لملف M3U لتسريع التحميل
- تحديث تلقائي كل 24 ساعة
- إمكانية التحديث اليدوي

### 🌐 دعم اللغة العربية
- واجهة باللغة العربية بالكامل
- دعم RTL (من اليمين إلى اليسار)
- خطوط عربية واضحة

## 🛠️ التقنيات المستخدمة

- **Flutter** - إطار العمل الأساسي
- **video_player & chewie** - مشغل الفيديو الداخلي
- **http** - لتحميل ملف M3U
- **shared_preferences** - للتخزين المحلي
- **url_launcher** - لفتح المشغلات الخارجية
- **provider** - إدارة الحالة

## 🚀 كيفية التشغيل

```bash
# تحميل المكتبات
flutter pub get

# تشغيل التطبيق
flutter run

# بناء APK
flutter build apk --release
```

## 📱 مصدر القنوات

```
https://raw.githubusercontent.com/hunter4ever007/hunter4ever007/master/m3u/hunter.m3u
```

---

**Hunter TV** - استمتع بمشاهدة قنواتك المفضلة! 📺✨
