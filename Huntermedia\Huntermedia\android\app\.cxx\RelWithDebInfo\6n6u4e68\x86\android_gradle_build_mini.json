{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\myapp\\huntertv\\huntertv\\huntertv\\android\\app\\.cxx\\RelWithDebInfo\\6n6u4e68\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\myapp\\huntertv\\huntertv\\huntertv\\android\\app\\.cxx\\RelWithDebInfo\\6n6u4e68\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}