import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/channel.dart';
import '../models/channel_group.dart';

class M3UService {
  static const String m3uUrl = 'https://raw.githubusercontent.com/hunter4ever007/hunter4ever007/master/m3u/hunter.m3u';

  static Future<List<ChannelGroup>> fetchChannels() async {
    try {
      final response = await http.get(Uri.parse(m3uUrl));
      
      if (response.statusCode == 200) {
        // Decode the response with UTF-8 encoding
        final content = utf8.decode(response.bodyBytes);
        return parseM3U(content);
      } else {
        throw Exception('Failed to load M3U file: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching M3U file: $e');
    }
  }

  static List<ChannelGroup> parseM3U(String content) {
    final lines = content.split('\n');
    final Map<String, List<Channel>> groupedChannels = {};
    
    for (int i = 0; i < lines.length; i++) {
      final line = lines[i].trim();
      
      // Check if this is an EXTINF line
      if (line.startsWith('#EXTINF:')) {
        // Get the next line which should be the URL
        if (i + 1 < lines.length) {
          final urlLine = lines[i + 1].trim();
          
          // Skip if URL line is empty or starts with #
          if (urlLine.isNotEmpty && !urlLine.startsWith('#')) {
            try {
              final channel = Channel.fromM3ULine(line, urlLine);
              final groupName = channel.groupTitle ?? 'عام';
              
              if (!groupedChannels.containsKey(groupName)) {
                groupedChannels[groupName] = [];
              }
              groupedChannels[groupName]!.add(channel);
            } catch (e) {
              // Skip malformed entries
              print('Error parsing channel: $e');
            }
          }
        }
      }
    }

    // Convert to ChannelGroup list and sort
    final groups = groupedChannels.entries
        .map((entry) => ChannelGroup(
              name: entry.key,
              channels: entry.value,
            ))
        .toList();

    // Sort groups by name, but put "عام" first
    groups.sort((a, b) {
      if (a.name == 'عام') return -1;
      if (b.name == 'عام') return 1;
      return a.name.compareTo(b.name);
    });

    return groups;
  }

  static String formatGroupName(String groupName) {
    // Clean up group names
    return groupName.trim().isEmpty ? 'عام' : groupName.trim();
  }
}
