# 📺 دليل استخدام Hunter TV على Android TV

## 🎮 التحكم بجهاز التحكم عن بعد

### ✅ الميزات المضافة لـ Android TV:

#### 🔧 **إعدادات Android TV:**
- ✅ دعم Android TV في AndroidManifest
- ✅ إضافة LEANBACK_LAUNCHER للظهور في قائمة Android TV
- ✅ دعم التنقل بدون لمس
- ✅ تحسين الواجهة للشاشات الكبيرة

#### 🎯 **التنقل بجهاز التحكم:**
- **الأسهم (↑↓←→)**: التنقل بين القنوات
- **زر OK/Enter**: تشغيل القناة المحددة
- **زر Back**: العودة للخلف
- **زر Menu**: فتح/إغلاق القائمة الجانبية

#### 📱 **الويدجت المحسنة:**
- **TVFocusableCard**: بطاقات قنوات قابلة للتركيز
- **TVSideMenu**: قائمة جانبية متوافقة مع التحكم عن بعد
- **تأثيرات بصرية**: إضاءة وحدود عند التركيز

## 🚀 **كيفية الاستخدام:**

### 1. **التشغيل الأول:**
- افتح التطبيق من قائمة Android TV
- انتظر تحميل القنوات تلقائياً
- استخدم الأسهم للتنقل بين القنوات

### 2. **تشغيل القنوات:**
- استخدم الأسهم للانتقال للقناة المطلوبة
- اضغط زر OK/Enter لتشغيل القناة
- سيفتح المشغل الداخلي تلقائياً

### 3. **التنقل في المشغل:**
- **زر Back**: العودة لقائمة القنوات
- **زر OK**: إظهار/إخفاء أزرار التحكم
- **الأسهم**: التحكم في مستوى الصوت والتقديم

### 4. **القائمة الجانبية:**
- **زر Menu**: فتح القائمة الجانبية
- **الأسهم**: التنقل بين الأقسام
- **زر OK**: اختيار القسم

## 🎨 **التحسينات البصرية:**

### **التركيز والإضاءة:**
- حدود زرقاء عند التركيز على القناة
- تأثير إضاءة حول العنصر المحدد
- تكبير طفيف للعنصر المركز عليه

### **الألوان والخطوط:**
- خلفية سوداء مناسبة للتلفزيون
- خطوط كبيرة وواضحة
- ألوان متباينة للوضوح

## 📋 **الملفات المحدثة:**

### **Android Manifest:**
```xml
<!-- Android TV support -->
<meta-data android:name="android.software.leanback" android:value="true" />
<uses-feature android:name="android.software.leanback" android:required="false" />
<uses-feature android:name="android.hardware.touchscreen" android:required="false" />
<category android:name="android.intent.category.LEANBACK_LAUNCHER"/>
```

### **الويدجت الجديدة:**
- `lib/widgets/tv_focusable_card.dart` - بطاقة قناة قابلة للتركيز
- `lib/widgets/tv_side_menu.dart` - قائمة جانبية للتلفزيون
- `lib/screens/tv_home_screen.dart` - شاشة رئيسية محسنة للتلفزيون

## 🔧 **إعدادات التحكم:**

### **أزرار جهاز التحكم المدعومة:**
- **D-Pad (الأسهم)**: التنقل الأساسي
- **OK/Select/Enter**: التأكيد والتشغيل
- **Back**: العودة للخلف
- **Menu**: فتح القوائم
- **Home**: العودة للشاشة الرئيسية

### **التنقل السريع:**
- **الضغط المطول على OK**: خيارات إضافية
- **الأسهم اليمين/اليسار**: التنقل السريع
- **الأسهم أعلى/أسفل**: التنقل العمودي

## 🎯 **نصائح للاستخدام الأمثل:**

### 1. **للحصول على أفضل تجربة:**
- استخدم شبكة Wi-Fi سريعة ومستقرة
- تأكد من تحديث التطبيق للإصدار الأحدث
- استخدم المشغل الداخلي للحصول على أفضل أداء

### 2. **حل المشاكل الشائعة:**
- إذا لم تعمل الأسهم: أعد تشغيل التطبيق
- إذا لم تظهر القنوات: تحقق من الاتصال بالإنترنت
- إذا لم يعمل التشغيل: جرب المشغل الخارجي

### 3. **تحسين الأداء:**
- أغلق التطبيقات الأخرى لتوفير الذاكرة
- استخدم كابل إيثرنت للاتصال المباشر
- تأكد من وجود مساحة تخزين كافية

## 📱 **التوافق:**

### **الأجهزة المدعومة:**
- ✅ Android TV (جميع الإصدارات)
- ✅ Google TV
- ✅ NVIDIA Shield TV
- ✅ Xiaomi Mi Box
- ✅ Amazon Fire TV (مع تثبيت يدوي)

### **أجهزة التحكم المدعومة:**
- ✅ جهاز التحكم الأصلي لـ Android TV
- ✅ أجهزة التحكم اللاسلكية
- ✅ تطبيقات التحكم عن بعد على الهاتف
- ✅ لوحة المفاتيح والماوس

---

## 🎉 **استمتع بمشاهدة قنواتك المفضلة على الشاشة الكبيرة!**

**Hunter TV** - محسن خصيصاً لتجربة Android TV المثالية! 📺✨
