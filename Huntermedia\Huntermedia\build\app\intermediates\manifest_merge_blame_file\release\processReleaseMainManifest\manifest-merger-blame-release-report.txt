1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.huntertv"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!-- Internet permission for downloading M3U files -->
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:3:5-67
11-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:3:22-64
12    <!-- Permission to query other apps for launching external players -->
13    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
13-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:5:5-77
13-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:5:22-74
14
15    <!-- Android TV features -->
16    <uses-feature
16-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:8:5-9:46
17        android:name="android.software.leanback"
17-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:8:19-59
18        android:required="false" />
18-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:9:19-43
19    <uses-feature
19-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:10:5-11:46
20        android:name="android.hardware.touchscreen"
20-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:10:19-62
21        android:required="false" />
21-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:11:19-43
22    <!--
23         Required to query activities that can process text, see:
24         https://developer.android.com/training/package-visibility and
25         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
26
27         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
28    -->
29    <queries>
29-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:55:5-79:15
30        <intent>
30-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:56:9-59:18
31            <action android:name="android.intent.action.PROCESS_TEXT" />
31-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:57:13-72
31-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:57:21-70
32
33            <data android:mimeType="text/plain" />
33-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:58:13-50
33-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:58:19-48
34        </intent>
35        <!-- Query for video players -->
36        <intent>
36-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:61:9-64:18
37            <action android:name="android.intent.action.VIEW" />
37-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:62:13-65
37-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:62:21-62
38
39            <data android:mimeType="video/*" />
39-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:58:13-50
39-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:58:19-48
40        </intent>
41        <!-- Query for streaming apps -->
42        <intent>
42-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:66:9-69:18
43            <action android:name="android.intent.action.VIEW" />
43-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:62:13-65
43-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:62:21-62
44
45            <data android:scheme="http" />
45-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:58:13-50
45-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:68:19-40
46        </intent>
47        <intent>
47-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:70:9-73:18
48            <action android:name="android.intent.action.VIEW" />
48-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:62:13-65
48-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:62:21-62
49
50            <data android:scheme="https" />
50-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:58:13-50
50-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:68:19-40
51        </intent>
52        <!-- VLC Player -->
53        <package android:name="org.videolan.vlc" />
53-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:75:9-52
53-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:75:18-49
54        <!-- MX Player -->
55        <package android:name="com.mxtech.videoplayer.ad" />
55-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:77:9-61
55-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:77:18-58
56        <package android:name="com.mxtech.videoplayer.pro" />
56-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:78:9-62
56-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:78:18-59
57    </queries>
58
59    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
59-->[androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f287878c588d535c6238ddf78c37b2ed\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:22:5-79
59-->[androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f287878c588d535c6238ddf78c37b2ed\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:22:22-76
60
61    <permission
61-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
62        android:name="com.example.huntertv.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
62-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
63        android:protectionLevel="signature" />
63-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
64
65    <uses-permission android:name="com.example.huntertv.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
65-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
65-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
66
67    <application
68        android:name="android.app.Application"
68-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:14:9-42
69        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
69-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
70        android:banner="@mipmap/ic_launcher"
70-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:16:9-45
71        android:extractNativeLibs="true"
72        android:icon="@mipmap/ic_launcher"
72-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:15:9-43
73        android:label="huntertv" >
73-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:13:9-33
74
75        <!-- Android TV support -->
76        <meta-data
76-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:19:9-20:43
77            android:name="android.software.leanback"
77-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:19:20-60
78            android:value="true" />
78-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:20:20-40
79
80        <activity
80-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:21:9-43:20
81            android:name="com.example.huntertv.MainActivity"
81-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:22:13-41
82            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
82-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:27:13-163
83            android:exported="true"
83-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:23:13-36
84            android:hardwareAccelerated="true"
84-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:28:13-47
85            android:launchMode="singleTop"
85-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:24:13-43
86            android:taskAffinity=""
86-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:25:13-36
87            android:theme="@style/LaunchTheme"
87-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:26:13-47
88            android:windowSoftInputMode="adjustResize" >
88-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:29:13-55
89
90            <!--
91                 Specifies an Android theme to apply to this Activity as soon as
92                 the Android process has started. This theme is visible to the user
93                 while the Flutter UI initializes. After that, this theme continues
94                 to determine the Window background behind the Flutter UI.
95            -->
96            <meta-data
96-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:34:13-37:17
97                android:name="io.flutter.embedding.android.NormalTheme"
97-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:35:15-70
98                android:resource="@style/NormalTheme" />
98-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:36:15-52
99
100            <intent-filter>
100-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:38:13-42:29
101                <action android:name="android.intent.action.MAIN" />
101-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:39:17-68
101-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:39:25-66
102
103                <category android:name="android.intent.category.LAUNCHER" />
103-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:40:17-76
103-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:40:27-74
104                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
104-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:41:17-85
104-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:41:27-83
105            </intent-filter>
106        </activity>
107        <!--
108             Don't delete the meta-data below.
109             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
110        -->
111        <meta-data
111-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:46:9-48:33
112            android:name="flutterEmbedding"
112-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:47:13-44
113            android:value="2" />
113-->C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\android\app\src\main\AndroidManifest.xml:48:13-30
114
115        <activity
115-->[:url_launcher_android] C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-11:74
116            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
116-->[:url_launcher_android] C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-74
117            android:exported="false"
117-->[:url_launcher_android] C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-37
118            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
118-->[:url_launcher_android] C:\Users\<USER>\Desktop\myapp\huntertv\huntertv\huntertv\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-71
119
120        <uses-library
120-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
121            android:name="androidx.window.extensions"
121-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
122            android:required="false" />
122-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
123        <uses-library
123-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
124            android:name="androidx.window.sidecar"
124-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
125            android:required="false" />
125-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
126
127        <provider
127-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
128            android:name="androidx.startup.InitializationProvider"
128-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
129            android:authorities="com.example.huntertv.androidx-startup"
129-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
130            android:exported="false" >
130-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
131            <meta-data
131-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
132                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
132-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
133                android:value="androidx.startup" />
133-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
134            <meta-data
134-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
135                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
136                android:value="androidx.startup" />
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
137        </provider>
138
139        <receiver
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
140            android:name="androidx.profileinstaller.ProfileInstallReceiver"
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
141            android:directBootAware="false"
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
142            android:enabled="true"
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
143            android:exported="true"
143-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
144            android:permission="android.permission.DUMP" >
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
145            <intent-filter>
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
146                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
147            </intent-filter>
148            <intent-filter>
148-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
149                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
150            </intent-filter>
151            <intent-filter>
151-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
152                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
152-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
152-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
153            </intent-filter>
154            <intent-filter>
154-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
155                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
156            </intent-filter>
157        </receiver>
158    </application>
159
160</manifest>
