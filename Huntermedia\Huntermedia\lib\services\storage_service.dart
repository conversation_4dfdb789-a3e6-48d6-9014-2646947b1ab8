import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/channel_group.dart';

class StorageService {
  static const String _channelsKey = 'cached_channels';
  static const String _lastUpdateKey = 'last_update';

  static Future<void> saveChannels(List<ChannelGroup> groups) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = jsonEncode(groups.map((group) => group.toJson()).toList());
      await prefs.setString(_channelsKey, jsonString);
      await prefs.setInt(_lastUpdateKey, DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      print('Error saving channels: $e');
    }
  }

  static Future<List<ChannelGroup>?> loadChannels() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_channelsKey);
      
      if (jsonString != null) {
        final List<dynamic> jsonList = jsonDecode(jsonString);
        return jsonList.map((json) => ChannelGroup.fromJson(json)).toList();
      }
    } catch (e) {
      print('Error loading channels: $e');
    }
    return null;
  }

  static Future<DateTime?> getLastUpdateTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestamp = prefs.getInt(_lastUpdateKey);
      if (timestamp != null) {
        return DateTime.fromMillisecondsSinceEpoch(timestamp);
      }
    } catch (e) {
      print('Error getting last update time: $e');
    }
    return null;
  }

  static Future<bool> shouldUpdate() async {
    final lastUpdate = await getLastUpdateTime();
    if (lastUpdate == null) return true;
    
    // Update if more than 24 hours have passed
    final now = DateTime.now();
    final difference = now.difference(lastUpdate);
    return difference.inHours >= 24;
  }

  static Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_channelsKey);
      await prefs.remove(_lastUpdateKey);
    } catch (e) {
      print('Error clearing cache: $e');
    }
  }
}
