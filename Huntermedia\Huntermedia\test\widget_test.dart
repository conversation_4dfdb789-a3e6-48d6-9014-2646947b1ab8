// This is a basic Flutter widget test for Hunter TV app.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:huntertv/main.dart';

void main() {
  testWidgets('Hunter TV app smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const HunterTVApp());

    // Verify that the app title is displayed.
    expect(find.text('Hunter TV'), findsOneWidget);

    // Verify that loading indicator appears initially
    expect(find.byType(CircularProgressIndicator), findsOneWidget);
  });
}
