import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:provider/provider.dart';
import '../models/vod_content.dart';
import '../providers/vod_provider.dart';

class EnhancedVodSection extends StatefulWidget {
  final VodType? initialType;

  const EnhancedVodSection({super.key, this.initialType});

  @override
  State<EnhancedVodSection> createState() => _EnhancedVodSectionState();
}

class _EnhancedVodSectionState extends State<EnhancedVodSection> {
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = Provider.of<VodProvider>(context, listen: false);
      if (widget.initialType != null) {
        provider.selectType(widget.initialType!);
      }
      provider.loadVodContent();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  // Responsive design helpers
  bool _isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < 600;
  }

  bool _isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= 600 && width < 1200;
  }

  bool _isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= 1200;
  }

  int _getCrossAxisCount(BuildContext context) {
    if (_isMobile(context)) {
      return 2; // 2 columns on mobile
    } else if (_isTablet(context)) {
      return 4; // 4 columns on tablet
    } else {
      return 6; // 6 columns on desktop/TV
    }
  }

  double _getChildAspectRatio(BuildContext context, VodType type) {
    if (_isMobile(context)) {
      return type == VodType.movie ? 0.6 : 0.7; // Taller on mobile
    } else if (_isTablet(context)) {
      return type == VodType.movie ? 0.65 : 0.75; // Medium on tablet
    } else {
      return type == VodType.movie ? 0.7 : 0.8; // Wider on desktop/TV
    }
  }

  double _getSpacing(BuildContext context) {
    if (_isMobile(context)) {
      return 8.0; // Smaller spacing on mobile
    } else if (_isTablet(context)) {
      return 12.0; // Medium spacing on tablet
    } else {
      return 16.0; // Larger spacing on desktop/TV
    }
  }

  EdgeInsets _getPadding(BuildContext context) {
    if (_isMobile(context)) {
      return const EdgeInsets.all(8.0); // Smaller padding on mobile
    } else if (_isTablet(context)) {
      return const EdgeInsets.all(12.0); // Medium padding on tablet
    } else {
      return const EdgeInsets.all(16.0); // Larger padding on desktop/TV
    }
  }

  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              Provider.of<VodProvider>(context, listen: false).selectedType ==
                      VodType.movie
                  ? 'البحث في الأفلام'
                  : 'البحث في المسلسلات',
            ),
            content: TextField(
              autofocus: true,
              decoration: const InputDecoration(
                hintText: 'اكتب اسم المحتوى...',
                prefixIcon: Icon(Icons.search),
              ),
              onChanged: (query) {
                Provider.of<VodProvider>(
                  context,
                  listen: false,
                ).searchContent(query);
              },
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إغلاق'),
              ),
            ],
          ),
    );
  }

  void _showFilterDialog(BuildContext context) {
    final provider = Provider.of<VodProvider>(context, listen: false);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تصفية المحتوى'),
            content: Consumer<VodProvider>(
              builder: (context, vodProvider, child) {
                return SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Favorites filter
                      CheckboxListTile(
                        title: const Text('المفضلة فقط'),
                        value: vodProvider.filter.favoritesOnly,
                        onChanged: (value) {
                          vodProvider.setFilter(
                            vodProvider.filter.copyWith(
                              favoritesOnly: value ?? false,
                            ),
                          );
                        },
                      ),

                      const Divider(),

                      // Genre filter
                      const Text(
                        'التصنيف:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      DropdownButtonFormField<String>(
                        value: vodProvider.filter.genre,
                        decoration: const InputDecoration(
                          hintText: 'اختر التصنيف',
                          border: OutlineInputBorder(),
                        ),
                        items: [
                          const DropdownMenuItem<String>(
                            value: null,
                            child: Text('جميع التصنيفات'),
                          ),
                          ...vodProvider.availableGenres.map(
                            (genre) => DropdownMenuItem<String>(
                              value: genre,
                              child: Text(genre),
                            ),
                          ),
                        ],
                        onChanged: (value) {
                          vodProvider.setFilter(
                            vodProvider.filter.copyWith(genre: value),
                          );
                        },
                      ),

                      const SizedBox(height: 16),

                      // Year range filter
                      const Text(
                        'نطاق السنة:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Expanded(
                            child: DropdownButtonFormField<int>(
                              value: vodProvider.filter.startYear,
                              decoration: const InputDecoration(
                                hintText: 'من سنة',
                                border: OutlineInputBorder(),
                              ),
                              items: [
                                const DropdownMenuItem<int>(
                                  value: null,
                                  child: Text('أي سنة'),
                                ),
                                ...vodProvider.availableYears.map(
                                  (year) => DropdownMenuItem<int>(
                                    value: year,
                                    child: Text(year.toString()),
                                  ),
                                ),
                              ],
                              onChanged: (value) {
                                vodProvider.setFilter(
                                  vodProvider.filter.copyWith(startYear: value),
                                );
                              },
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: DropdownButtonFormField<int>(
                              value: vodProvider.filter.endYear,
                              decoration: const InputDecoration(
                                hintText: 'إلى سنة',
                                border: OutlineInputBorder(),
                              ),
                              items: [
                                const DropdownMenuItem<int>(
                                  value: null,
                                  child: Text('أي سنة'),
                                ),
                                ...vodProvider.availableYears.map(
                                  (year) => DropdownMenuItem<int>(
                                    value: year,
                                    child: Text(year.toString()),
                                  ),
                                ),
                              ],
                              onChanged: (value) {
                                vodProvider.setFilter(
                                  vodProvider.filter.copyWith(endYear: value),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                );
              },
            ),
            actions: [
              TextButton(
                onPressed: () {
                  provider.clearFilters();
                  Navigator.pop(context);
                },
                child: const Text('مسح الفلاتر'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إغلاق'),
              ),
            ],
          ),
    );
  }

  void _showSortDialog(BuildContext context) {
    final provider = Provider.of<VodProvider>(context, listen: false);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('ترتيب المحتوى'),
            content: Consumer<VodProvider>(
              builder: (context, vodProvider, child) {
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  children:
                      VodSortOption.values.map((option) {
                        return RadioListTile<VodSortOption>(
                          title: Text(vodProvider.getSortOptionLabel(option)),
                          value: option,
                          groupValue: vodProvider.sortOption,
                          onChanged: (value) {
                            if (value != null) {
                              vodProvider.setSortOption(value);
                              Navigator.pop(context);
                            }
                          },
                        );
                      }).toList(),
                );
              },
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إغلاق'),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<VodProvider>(
      builder: (context, provider, child) {
        final isMobile = _isMobile(context);
        final padding = _getPadding(context);

        return Column(
          children: [
            // Top controls bar
            Container(
              padding: padding,
              color: Colors.black,
              child: Column(
                children: [
                  // Search bar or title with action buttons
                  if (isMobile)
                    ..._buildMobileHeader(provider)
                  else
                    ..._buildDesktopHeader(provider),

                  SizedBox(height: isMobile ? 12 : 16),

                  // Type buttons
                  if (isMobile)
                    _buildMobileTypeButtons(provider)
                  else
                    _buildDesktopTypeButtons(provider),

                  SizedBox(height: isMobile ? 8 : 12),

                  // Stats and favorites
                  _buildStatsRow(provider),
                ],
              ),
            ),

            // Content
            Expanded(child: _buildContent(provider)),
          ],
        );
      },
    );
  }

  List<Widget> _buildMobileHeader(VodProvider provider) {
    return [
      // Title
      if (!_isSearching)
        Text(
          provider.selectedType == VodType.movie ? 'الأفلام' : 'المسلسلات',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),

      // Search field (when searching)
      if (_isSearching)
        TextField(
          controller: _searchController,
          autofocus: true,
          style: const TextStyle(color: Colors.white),
          decoration: const InputDecoration(
            hintText: 'البحث في المحتوى...',
            hintStyle: TextStyle(color: Colors.white70),
            border: InputBorder.none,
            prefixIcon: Icon(Icons.search, color: Colors.white70),
          ),
          onChanged: (query) => provider.searchContent(query),
        ),

      const SizedBox(height: 8),

      // Action buttons row
      Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildMobileActionButton(
            icon: _isSearching ? Icons.close : Icons.search,
            label: _isSearching ? 'إغلاق' : 'بحث',
            autofocus: true, // First button gets focus
            onPressed: () {
              setState(() {
                _isSearching = !_isSearching;
                if (!_isSearching) {
                  _searchController.clear();
                  provider.searchContent('');
                }
              });
            },
          ),
          _buildMobileActionButton(
            icon: Icons.filter_list,
            label: 'تصفية',
            hasIndicator: provider.filter.hasActiveFilters,
            onPressed: () => _showFilterDialog(context),
          ),
          _buildMobileActionButton(
            icon: Icons.sort,
            label: 'ترتيب',
            onPressed: () => _showSortDialog(context),
          ),
          _buildMobileActionButton(
            icon: Icons.refresh,
            label: 'تحديث',
            onPressed:
                provider.isLoading
                    ? null
                    : () async {
                      await provider.refreshVodContent();
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('تم تحديث المحتوى بنجاح'),
                          ),
                        );
                      }
                    },
          ),
        ],
      ),
    ];
  }

  List<Widget> _buildDesktopHeader(VodProvider provider) {
    return [
      Row(
        children: [
          // Title or search field
          Expanded(
            child:
                _isSearching
                    ? TextField(
                      controller: _searchController,
                      autofocus: true,
                      style: const TextStyle(color: Colors.white),
                      decoration: const InputDecoration(
                        hintText: 'البحث في المحتوى...',
                        hintStyle: TextStyle(color: Colors.white70),
                        border: InputBorder.none,
                        prefixIcon: Icon(Icons.search, color: Colors.white70),
                      ),
                      onChanged: (query) => provider.searchContent(query),
                    )
                    : Text(
                      provider.selectedType == VodType.movie
                          ? 'الأفلام'
                          : 'المسلسلات',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
          ),

          // Action buttons
          Row(
            children: [
              IconButton(
                icon: Icon(
                  _isSearching ? Icons.close : Icons.search,
                  color: Colors.white,
                ),
                onPressed: () {
                  setState(() {
                    _isSearching = !_isSearching;
                    if (!_isSearching) {
                      _searchController.clear();
                      provider.searchContent('');
                    }
                  });
                },
              ),
              IconButton(
                icon: Stack(
                  children: [
                    const Icon(Icons.filter_list, color: Colors.white),
                    if (provider.filter.hasActiveFilters)
                      Positioned(
                        right: 0,
                        top: 0,
                        child: Container(
                          width: 8,
                          height: 8,
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                  ],
                ),
                onPressed: () => _showFilterDialog(context),
              ),
              IconButton(
                icon: const Icon(Icons.sort, color: Colors.white),
                onPressed: () => _showSortDialog(context),
              ),
              IconButton(
                icon: const Icon(Icons.refresh, color: Colors.white),
                onPressed:
                    provider.isLoading
                        ? null
                        : () async {
                          await provider.refreshVodContent();
                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('تم تحديث المحتوى بنجاح'),
                              ),
                            );
                          }
                        },
              ),
            ],
          ),
        ],
      ),
    ];
  }

  Widget _buildMobileActionButton({
    required IconData icon,
    required String label,
    required VoidCallback? onPressed,
    bool hasIndicator = false,
    bool autofocus = false,
  }) {
    return _TVFocusableButton(
      autofocus: autofocus,
      onPressed: onPressed,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            children: [
              Icon(icon, color: Colors.white, size: 20),
              if (hasIndicator)
                Positioned(
                  right: 0,
                  top: 0,
                  child: Container(
                    width: 6,
                    height: 6,
                    decoration: const BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(color: Colors.white70, fontSize: 10),
          ),
        ],
      ),
    );
  }

  Widget _buildDesktopActionButton({
    required IconData icon,
    required VoidCallback? onPressed,
    bool hasIndicator = false,
    bool autofocus = false,
  }) {
    return _TVFocusableButton(
      autofocus: autofocus,
      onPressed: onPressed,
      padding: const EdgeInsets.all(8),
      child: Stack(
        children: [
          Icon(icon, color: Colors.white),
          if (hasIndicator)
            Positioned(
              right: 0,
              top: 0,
              child: Container(
                width: 8,
                height: 8,
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildMobileTypeButtons(VodProvider provider) {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: _buildTypeButton(
            provider,
            VodType.movie,
            'أفلام (${provider.totalMoviesCount})',
            Icons.movie,
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          width: double.infinity,
          child: _buildTypeButton(
            provider,
            VodType.series,
            'مسلسلات (${provider.totalSeriesCount})',
            Icons.tv,
          ),
        ),
      ],
    );
  }

  Widget _buildDesktopTypeButtons(VodProvider provider) {
    return Row(
      children: [
        Expanded(
          child: _buildTypeButton(
            provider,
            VodType.movie,
            'أفلام (${provider.totalMoviesCount})',
            Icons.movie,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildTypeButton(
            provider,
            VodType.series,
            'مسلسلات (${provider.totalSeriesCount})',
            Icons.tv,
          ),
        ),
      ],
    );
  }

  Widget _buildStatsRow(VodProvider provider) {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'المفضلة',
            provider.favoritesCount.toString(),
            Icons.favorite,
            Colors.red,
            () {
              provider.setFilter(provider.filter.copyWith(favoritesOnly: true));
            },
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildStatCard(
            'المجموع',
            provider.selectedGroupContent.length.toString(),
            Icons.video_library,
            Colors.blue,
            null,
          ),
        ),
      ],
    );
  }

  Widget _buildTypeButton(
    VodProvider provider,
    VodType type,
    String label,
    IconData icon,
  ) {
    final isSelected = provider.selectedType == type;
    return ElevatedButton.icon(
      onPressed: () => provider.selectType(type),
      icon: Icon(icon),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor:
            isSelected ? Theme.of(context).primaryColor : Colors.grey[700],
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 12),
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
    VoidCallback? onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Card(
        color: Colors.grey[900],
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(height: 4),
              Text(
                value,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                title,
                style: const TextStyle(color: Colors.white70, fontSize: 12),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContent(VodProvider provider) {
    if (provider.isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: Colors.white),
            SizedBox(height: 16),
            Text(
              'جاري تحميل المحتوى...',
              style: TextStyle(color: Colors.white, fontSize: 16),
            ),
          ],
        ),
      );
    }

    if (provider.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 64),
            const SizedBox(height: 16),
            const Text(
              'خطأ في تحميل المحتوى',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              provider.error!,
              style: const TextStyle(color: Colors.white70),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => provider.loadVodContent(forceRefresh: true),
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    final content = provider.selectedGroupContent;

    if (content.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              provider.selectedType == VodType.movie ? Icons.movie : Icons.tv,
              color: Colors.white54,
              size: 64,
            ),
            const SizedBox(height: 16),
            Text(
              provider.searchQuery.isNotEmpty
                  ? 'لا توجد نتائج للبحث'
                  : provider.filter.hasActiveFilters
                  ? 'لا توجد نتائج للفلتر المحدد'
                  : provider.selectedType == VodType.movie
                  ? 'لا توجد أفلام متاحة'
                  : 'لا توجد مسلسلات متاحة',
              style: const TextStyle(color: Colors.white, fontSize: 18),
            ),
            if (provider.filter.hasActiveFilters) ...[
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => provider.clearFilters(),
                child: const Text('مسح الفلاتر'),
              ),
            ],
          ],
        ),
      );
    }

    final spacing = _getSpacing(context);
    final padding = _getPadding(context);
    final crossAxisCount = _getCrossAxisCount(context);
    final aspectRatio = _getChildAspectRatio(context, provider.selectedType);

    return Padding(
      padding: padding,
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          childAspectRatio: aspectRatio,
          crossAxisSpacing: spacing,
          mainAxisSpacing: spacing,
        ),
        itemCount: content.length,
        itemBuilder: (context, index) {
          final item = content[index];
          return _TVVodCard(
            content: item,
            isFavorite: provider.isFavorite(item),
            onTap: () => provider.playContent(item, context),
            onFavoriteToggle: () => provider.toggleFavorite(item),
            isMobile: _isMobile(context),
            autofocus: index == 0, // First item gets autofocus
          );
        },
      ),
    );
  }
}

class _TVVodCard extends StatefulWidget {
  final VodContent content;
  final bool isFavorite;
  final VoidCallback onTap;
  final VoidCallback onFavoriteToggle;
  final bool isMobile;
  final bool autofocus;

  const _TVVodCard({
    required this.content,
    required this.isFavorite,
    required this.onTap,
    required this.onFavoriteToggle,
    this.isMobile = false,
    this.autofocus = false,
  });

  @override
  State<_TVVodCard> createState() => _TVVodCardState();
}

class _TVVodCardState extends State<_TVVodCard> {
  bool _isFocused = false;
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(() {
      setState(() {
        _isFocused = _focusNode.hasFocus;
      });
    });
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Focus(
      focusNode: _focusNode,
      autofocus: widget.autofocus,
      onKeyEvent: (node, event) {
        if (event is KeyDownEvent) {
          if (event.logicalKey == LogicalKeyboardKey.select ||
              event.logicalKey == LogicalKeyboardKey.enter ||
              event.logicalKey == LogicalKeyboardKey.space) {
            widget.onTap();
            return KeyEventResult.handled;
          }
          // Add favorite toggle with F key
          if (event.logicalKey == LogicalKeyboardKey.keyF) {
            widget.onFavoriteToggle();
            return KeyEventResult.handled;
          }
        }
        return KeyEventResult.ignored;
      },
      child: GestureDetector(
        onTap: widget.onTap,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          transform: Matrix4.identity()..scale(_isFocused ? 1.1 : 1.0),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border:
                _isFocused
                    ? Border.all(
                      color: Theme.of(context).primaryColor,
                      width: 3,
                    )
                    : null,
            boxShadow:
                _isFocused
                    ? [
                      BoxShadow(
                        color: Theme.of(
                          context,
                        ).primaryColor.withValues(alpha: 0.4),
                        blurRadius: 16,
                        spreadRadius: 6,
                      ),
                    ]
                    : [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
          ),
          child: Card(
            elevation: _isFocused ? 16 : 4,
            margin: EdgeInsets.zero,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            clipBehavior: Clip.antiAlias,
            child: Stack(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Poster
                    Expanded(
                      flex: 4,
                      child: Container(
                        decoration: BoxDecoration(
                          color:
                              _isFocused
                                  ? Theme.of(
                                    context,
                                  ).primaryColor.withValues(alpha: 0.1)
                                  : Colors.grey[900],
                        ),
                        child:
                            widget.content.posterUrl != null
                                ? CachedNetworkImage(
                                  imageUrl: widget.content.posterUrl!,
                                  fit: BoxFit.cover,
                                  placeholder:
                                      (context, url) => Container(
                                        color: Colors.grey[800],
                                        child: const Center(
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            color: Colors.white54,
                                          ),
                                        ),
                                      ),
                                  errorWidget:
                                      (context, url, error) => Container(
                                        color: Colors.grey[800],
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Icon(
                                              widget.content.type ==
                                                      VodType.movie
                                                  ? Icons.movie
                                                  : Icons.tv,
                                              color: Colors.white54,
                                              size: widget.isMobile ? 30 : 40,
                                            ),
                                            const SizedBox(height: 8),
                                            Text(
                                              widget.content.type ==
                                                      VodType.movie
                                                  ? 'فيلم'
                                                  : 'مسلسل',
                                              style: TextStyle(
                                                color: Colors.white54,
                                                fontSize:
                                                    widget.isMobile ? 10 : 12,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                )
                                : Container(
                                  color: Colors.grey[800],
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        widget.content.type == VodType.movie
                                            ? Icons.movie
                                            : Icons.tv,
                                        color: Colors.white54,
                                        size: widget.isMobile ? 30 : 40,
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        widget.content.type == VodType.movie
                                            ? 'فيلم'
                                            : 'مسلسل',
                                        style: TextStyle(
                                          color: Colors.white54,
                                          fontSize: widget.isMobile ? 10 : 12,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                      ),
                    ),

                    // Info
                    Expanded(
                      flex: 1,
                      child: Container(
                        padding: EdgeInsets.all(widget.isMobile ? 6 : 8),
                        decoration: BoxDecoration(
                          color:
                              _isFocused
                                  ? Theme.of(
                                    context,
                                  ).primaryColor.withValues(alpha: 0.2)
                                  : Colors.grey[900],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              widget.content.cleanTitle,
                              style: TextStyle(
                                color: _isFocused ? Colors.white : Colors.white,
                                fontSize: widget.isMobile ? 10 : 11,
                                fontWeight: FontWeight.bold,
                              ),
                              maxLines: widget.isMobile ? 1 : 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            if (widget.content.year != null &&
                                !widget.isMobile) ...[
                              const SizedBox(height: 2),
                              Row(
                                children: [
                                  Icon(
                                    Icons.calendar_today,
                                    size: 8,
                                    color:
                                        _isFocused
                                            ? Colors.white
                                            : Colors.white70,
                                  ),
                                  const SizedBox(width: 2),
                                  Text(
                                    widget.content.year!,
                                    style: TextStyle(
                                      color:
                                          _isFocused
                                              ? Colors.white
                                              : Colors.white70,
                                      fontSize: 9,
                                    ),
                                  ),
                                  const Spacer(),
                                  Icon(
                                    widget.content.type == VodType.movie
                                        ? Icons.movie
                                        : Icons.tv,
                                    size: 8,
                                    color:
                                        _isFocused
                                            ? Colors.white
                                            : Colors.white70,
                                  ),
                                ],
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                  ],
                ),

                // Favorite button
                Positioned(
                  top: widget.isMobile ? 4 : 8,
                  right: widget.isMobile ? 4 : 8,
                  child: GestureDetector(
                    onTap: widget.onFavoriteToggle,
                    child: Container(
                      padding: EdgeInsets.all(widget.isMobile ? 3 : 4),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.7),
                        shape: BoxShape.circle,
                        border:
                            _isFocused
                                ? Border.all(color: Colors.white, width: 1)
                                : null,
                      ),
                      child: Icon(
                        widget.isFavorite
                            ? Icons.favorite
                            : Icons.favorite_border,
                        color: widget.isFavorite ? Colors.red : Colors.white,
                        size: widget.isMobile ? 14 : 16,
                      ),
                    ),
                  ),
                ),

                // Focus indicator overlay
                if (_isFocused)
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.white, width: 2),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// TV Focusable Button Widget
class _TVFocusableButton extends StatefulWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final bool autofocus;
  final EdgeInsets? padding;
  final Color? backgroundColor;
  final Color? focusColor;
  final BorderRadius? borderRadius;

  const _TVFocusableButton({
    required this.child,
    this.onPressed,
    this.autofocus = false,
    this.padding,
    this.backgroundColor,
    this.focusColor,
    this.borderRadius,
  });

  @override
  State<_TVFocusableButton> createState() => _TVFocusableButtonState();
}

class _TVFocusableButtonState extends State<_TVFocusableButton> {
  bool _isFocused = false;
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(() {
      setState(() {
        _isFocused = _focusNode.hasFocus;
      });
    });
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Focus(
      focusNode: _focusNode,
      autofocus: widget.autofocus,
      onKeyEvent: (node, event) {
        if (event is KeyDownEvent && widget.onPressed != null) {
          if (event.logicalKey == LogicalKeyboardKey.select ||
              event.logicalKey == LogicalKeyboardKey.enter ||
              event.logicalKey == LogicalKeyboardKey.space) {
            widget.onPressed!();
            return KeyEventResult.handled;
          }
        }
        return KeyEventResult.ignored;
      },
      child: GestureDetector(
        onTap: widget.onPressed,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: widget.padding ?? const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color:
                _isFocused
                    ? (widget.focusColor ?? Theme.of(context).primaryColor)
                    : (widget.backgroundColor ?? Colors.transparent),
            borderRadius: widget.borderRadius ?? BorderRadius.circular(8),
            border:
                _isFocused ? Border.all(color: Colors.white, width: 2) : null,
            boxShadow:
                _isFocused
                    ? [
                      BoxShadow(
                        color: Theme.of(
                          context,
                        ).primaryColor.withValues(alpha: 0.4),
                        blurRadius: 12,
                        spreadRadius: 4,
                      ),
                    ]
                    : null,
          ),
          child: widget.child,
        ),
      ),
    );
  }
}

// TV Focusable Button Widget
class _TVFocusableButton extends StatefulWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final bool autofocus;
  final EdgeInsets? padding;
  final Color? backgroundColor;
  final Color? focusColor;
  final BorderRadius? borderRadius;

  const _TVFocusableButton({
    required this.child,
    this.onPressed,
    this.autofocus = false,
    this.padding,
    this.backgroundColor,
    this.focusColor,
    this.borderRadius,
  });

  @override
  State<_TVFocusableButton> createState() => _TVFocusableButtonState();
}

class _TVFocusableButtonState extends State<_TVFocusableButton> {
  bool _isFocused = false;
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(() {
      setState(() {
        _isFocused = _focusNode.hasFocus;
      });
    });
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Focus(
      focusNode: _focusNode,
      autofocus: widget.autofocus,
      onKeyEvent: (node, event) {
        if (event is KeyDownEvent && widget.onPressed != null) {
          if (event.logicalKey == LogicalKeyboardKey.select ||
              event.logicalKey == LogicalKeyboardKey.enter ||
              event.logicalKey == LogicalKeyboardKey.space) {
            widget.onPressed!();
            return KeyEventResult.handled;
          }
        }
        return KeyEventResult.ignored;
      },
      child: GestureDetector(
        onTap: widget.onPressed,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: widget.padding ?? const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color:
                _isFocused
                    ? (widget.focusColor ?? Theme.of(context).primaryColor)
                    : (widget.backgroundColor ?? Colors.transparent),
            borderRadius: widget.borderRadius ?? BorderRadius.circular(8),
            border:
                _isFocused ? Border.all(color: Colors.white, width: 2) : null,
            boxShadow:
                _isFocused
                    ? [
                      BoxShadow(
                        color: Theme.of(
                          context,
                        ).primaryColor.withValues(alpha: 0.4),
                        blurRadius: 12,
                        spreadRadius: 4,
                      ),
                    ]
                    : null,
          ),
          child: widget.child,
        ),
      ),
    );
  }
}
