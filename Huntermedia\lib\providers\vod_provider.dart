import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/vod_content.dart';
import '../models/channel.dart';
import '../services/vod_service.dart';
import '../screens/video_player_screen.dart';

// PlayerType enum for VOD
enum PlayerType { internal, external }

class VodProvider with ChangeNotifier {
  List<VodGroup> _vodGroups = [];
  List<VodContent> _searchResults = [];
  bool _isLoading = false;
  String? _error;
  String _selectedGroupName = '';
  String _searchQuery = '';
  VodType _selectedType = VodType.movie;
  PlayerType _preferredPlayer = PlayerType.internal;

  List<VodGroup> get vodGroups => _vodGroups;
  List<VodContent> get searchResults => _searchResults;
  bool get isLoading => _isLoading;
  String? get error => _error;
  String get selectedGroupName => _selectedGroupName;
  String get searchQuery => _searchQuery;
  VodType get selectedType => _selectedType;
  PlayerType get preferredPlayer => _preferredPlayer;

  List<String> get groupNames =>
      _vodGroups
          .where((group) => group.type == _selectedType)
          .map((group) => group.name)
          .toList();

  List<VodContent> get selectedGroupContent {
    if (_selectedGroupName.isEmpty) {
      // Return all content of selected type
      return _vodGroups
          .where((group) => group.type == _selectedType)
          .expand((group) => group.content)
          .where((content) => content.type == _selectedType)
          .toList();
    }

    final group = _vodGroups.firstWhere(
      (group) =>
          group.name == _selectedGroupName && group.type == _selectedType,
      orElse: () => VodGroup(name: '', content: [], type: _selectedType),
    );
    return group.content;
  }

  List<VodContent> get allMovies => VodService.getMoviesFromGroups(_vodGroups);
  List<VodContent> get allSeries => VodService.getSeriesFromGroups(_vodGroups);

  int get totalMoviesCount => allMovies.length;
  int get totalSeriesCount => allSeries.length;
  int get totalContentCount => totalMoviesCount + totalSeriesCount;

  Future<void> loadVodContent({bool forceRefresh = false}) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Check if we should load from cache or fetch new data
      if (!forceRefresh) {
        final cachedContent = await _loadCachedVodContent();
        final shouldUpdate = await _shouldUpdateVodContent();

        if (cachedContent != null && !shouldUpdate) {
          _vodGroups = cachedContent;
          _isLoading = false;
          notifyListeners();
          return;
        }
      }

      // Fetch new data
      final groups = await VodService.fetchVodContent();
      _vodGroups = groups;

      // Save to cache
      await _saveVodContentToCache(groups);
    } catch (e) {
      _error = e.toString();

      // Try to load from cache as fallback
      final cachedContent = await _loadCachedVodContent();
      if (cachedContent != null) {
        _vodGroups = cachedContent;
      }
    }

    _isLoading = false;
    notifyListeners();
  }

  void selectType(VodType type) {
    _selectedType = type;
    _selectedGroupName = '';
    _searchQuery = '';
    _searchResults.clear();
    notifyListeners();
  }

  void selectGroup(String groupName) {
    _selectedGroupName = groupName;
    _searchQuery = '';
    _searchResults.clear();
    notifyListeners();
  }

  void searchContent(String query) {
    _searchQuery = query;

    if (query.isEmpty) {
      _searchResults.clear();
    } else {
      _searchResults =
          VodService.searchContent(
            _vodGroups,
            query,
          ).where((content) => content.type == _selectedType).toList();
    }

    notifyListeners();
  }

  Future<void> refreshVodContent() async {
    await loadVodContent(forceRefresh: true);
  }

  void setPreferredPlayer(PlayerType playerType) {
    _preferredPlayer = playerType;
    _savePlayerPreference();
    notifyListeners();
  }

  Future<void> playContent(VodContent content, BuildContext context) async {
    try {
      if (_preferredPlayer == PlayerType.internal) {
        // Use internal player - create a Channel object from VodContent
        final channel = Channel(
          name: content.name,
          url: content.url,
          logo: content.logo,
          groupTitle: content.groupTitle,
          tvgId: content.tvgId,
          tvgName: content.tvgName,
        );

        if (context.mounted) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => VideoPlayerScreen(channel: channel),
            ),
          );
        }
      } else {
        // Use external player
        final uri = Uri.parse(content.url);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
        } else {
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('لا يمكن فتح المشغل الخارجي'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تشغيل المحتوى: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Cache management methods
  Future<List<VodGroup>?> _loadCachedVodContent() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString('cached_vod_content');

      if (jsonString != null) {
        final List<dynamic> jsonList = jsonDecode(jsonString);
        return jsonList.map((json) => VodGroup.fromJson(json)).toList();
      }
    } catch (e) {
      print('Error loading cached VOD content: $e');
    }
    return null;
  }

  Future<void> _saveVodContentToCache(List<VodGroup> groups) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = jsonEncode(
        groups.map((group) => group.toJson()).toList(),
      );
      await prefs.setString('cached_vod_content', jsonString);
      await prefs.setInt(
        'vod_last_update',
        DateTime.now().millisecondsSinceEpoch,
      );
    } catch (e) {
      print('Error saving VOD content to cache: $e');
    }
  }

  Future<bool> _shouldUpdateVodContent() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestamp = prefs.getInt('vod_last_update');
      if (timestamp != null) {
        final lastUpdate = DateTime.fromMillisecondsSinceEpoch(timestamp);
        final now = DateTime.now();
        final difference = now.difference(lastUpdate);
        return difference.inHours >= 24; // Update every 24 hours
      }
    } catch (e) {
      print('Error checking VOD update time: $e');
    }
    return true;
  }

  Future<void> _savePlayerPreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('preferred_player', _preferredPlayer.toString());
    } catch (e) {
      print('Error saving player preference: $e');
    }
  }

  Future<void> _loadPlayerPreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final playerString = prefs.getString('preferred_player');
      if (playerString != null) {
        _preferredPlayer = PlayerType.values.firstWhere(
          (type) => type.toString() == playerString,
          orElse: () => PlayerType.internal,
        );
      }
    } catch (e) {
      print('Error loading player preference: $e');
    }
  }
}
