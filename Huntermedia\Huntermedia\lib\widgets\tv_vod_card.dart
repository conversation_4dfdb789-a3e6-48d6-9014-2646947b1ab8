import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../models/vod_content.dart';

class TVVodCard extends StatefulWidget {
  final VodContent content;
  final bool isFavorite;
  final VoidCallback onTap;
  final VoidCallback onFavoriteToggle;
  final bool autofocus;
  final bool isMobile;

  const TVVodCard({
    super.key,
    required this.content,
    required this.isFavorite,
    required this.onTap,
    required this.onFavoriteToggle,
    this.autofocus = false,
    this.isMobile = false,
  });

  @override
  State<TVVodCard> createState() => _TVVodCardState();
}

class _TVVodCardState extends State<TVVodCard> {
  bool _isFocused = false;
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(() {
      setState(() {
        _isFocused = _focusNode.hasFocus;
      });
    });
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Focus(
      focusNode: _focusNode,
      autofocus: widget.autofocus,
      onKeyEvent: (node, event) {
        if (event is KeyDownEvent) {
          if (event.logicalKey == LogicalKeyboardKey.select ||
              event.logicalKey == LogicalKeyboardKey.enter ||
              event.logicalKey == LogicalKeyboardKey.space) {
            widget.onTap();
            return KeyEventResult.handled;
          }
          // Add favorite toggle with specific key
          if (event.logicalKey == LogicalKeyboardKey.keyF ||
              event.logicalKey == LogicalKeyboardKey.star) {
            widget.onFavoriteToggle();
            return KeyEventResult.handled;
          }
        }
        return KeyEventResult.ignored;
      },
      child: GestureDetector(
        onTap: widget.onTap,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          transform: Matrix4.identity()
            ..scale(_isFocused ? 1.1 : 1.0),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: _isFocused
                ? Border.all(
                    color: Theme.of(context).primaryColor,
                    width: 3,
                  )
                : null,
            boxShadow: _isFocused
                ? [
                    BoxShadow(
                      color: Theme.of(context).primaryColor.withValues(alpha: 0.4),
                      blurRadius: 16,
                      spreadRadius: 6,
                    ),
                  ]
                : [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
          ),
          child: Card(
            elevation: _isFocused ? 16 : 4,
            margin: EdgeInsets.zero,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            clipBehavior: Clip.antiAlias,
            child: Stack(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Poster
                    Expanded(
                      flex: 4,
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.grey[900],
                        ),
                        child: widget.content.posterUrl != null
                            ? CachedNetworkImage(
                                imageUrl: widget.content.posterUrl!,
                                fit: BoxFit.cover,
                                placeholder: (context, url) => Container(
                                  color: Colors.grey[800],
                                  child: const Center(
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      color: Colors.white54,
                                    ),
                                  ),
                                ),
                                errorWidget: (context, url, error) => Container(
                                  color: Colors.grey[800],
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        widget.content.type == VodType.movie
                                            ? Icons.movie
                                            : Icons.tv,
                                        color: Colors.white54,
                                        size: widget.isMobile ? 30 : 40,
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        widget.content.type == VodType.movie
                                            ? 'فيلم'
                                            : 'مسلسل',
                                        style: TextStyle(
                                          color: Colors.white54,
                                          fontSize: widget.isMobile ? 10 : 12,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              )
                            : Container(
                                color: Colors.grey[800],
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      widget.content.type == VodType.movie
                                          ? Icons.movie
                                          : Icons.tv,
                                      color: Colors.white54,
                                      size: widget.isMobile ? 30 : 40,
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      widget.content.type == VodType.movie
                                          ? 'فيلم'
                                          : 'مسلسل',
                                      style: TextStyle(
                                        color: Colors.white54,
                                        fontSize: widget.isMobile ? 10 : 12,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                      ),
                    ),

                    // Info
                    Expanded(
                      flex: 1,
                      child: Container(
                        padding: EdgeInsets.all(widget.isMobile ? 6 : 8),
                        decoration: BoxDecoration(
                          color: _isFocused
                              ? Theme.of(context).primaryColor.withValues(alpha: 0.2)
                              : Colors.grey[900],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              widget.content.cleanTitle,
                              style: TextStyle(
                                color: _isFocused
                                    ? Colors.white
                                    : Colors.white,
                                fontSize: widget.isMobile ? 10 : 11,
                                fontWeight: FontWeight.bold,
                              ),
                              maxLines: widget.isMobile ? 1 : 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            if (widget.content.year != null && !widget.isMobile) ...[
                              const SizedBox(height: 2),
                              Row(
                                children: [
                                  Icon(
                                    Icons.calendar_today,
                                    size: 8,
                                    color: _isFocused
                                        ? Colors.white
                                        : Colors.white70,
                                  ),
                                  const SizedBox(width: 2),
                                  Text(
                                    widget.content.year!,
                                    style: TextStyle(
                                      color: _isFocused
                                          ? Colors.white
                                          : Colors.white70,
                                      fontSize: 9,
                                    ),
                                  ),
                                  const Spacer(),
                                  Icon(
                                    widget.content.type == VodType.movie
                                        ? Icons.movie
                                        : Icons.tv,
                                    size: 8,
                                    color: _isFocused
                                        ? Colors.white
                                        : Colors.white70,
                                  ),
                                ],
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                
                // Favorite button
                Positioned(
                  top: widget.isMobile ? 4 : 8,
                  right: widget.isMobile ? 4 : 8,
                  child: GestureDetector(
                    onTap: widget.onFavoriteToggle,
                    child: Container(
                      padding: EdgeInsets.all(widget.isMobile ? 3 : 4),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.7),
                        shape: BoxShape.circle,
                        border: _isFocused
                            ? Border.all(color: Colors.white, width: 1)
                            : null,
                      ),
                      child: Icon(
                        widget.isFavorite ? Icons.favorite : Icons.favorite_border,
                        color: widget.isFavorite ? Colors.red : Colors.white,
                        size: widget.isMobile ? 14 : 16,
                      ),
                    ),
                  ),
                ),

                // Focus indicator overlay
                if (_isFocused)
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.white,
                          width: 2,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
