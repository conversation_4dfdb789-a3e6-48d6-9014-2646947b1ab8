import 'channel.dart';

class ChannelGroup {
  final String name;
  final List<Channel> channels;

  ChannelGroup({
    required this.name,
    required this.channels,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'channels': channels.map((channel) => channel.toJson()).toList(),
    };
  }

  factory ChannelGroup.fromJson(Map<String, dynamic> json) {
    return ChannelGroup(
      name: json['name'] ?? '',
      channels: (json['channels'] as List<dynamic>?)
              ?.map((channelJson) => Channel.fromJson(channelJson))
              .toList() ??
          [],
    );
  }
}
