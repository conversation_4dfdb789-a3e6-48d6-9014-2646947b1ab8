import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../providers/channels_provider.dart';
import '../models/vod_content.dart';
import '../widgets/enhanced_vod_section.dart';

class TVSideMenu extends StatefulWidget {
  const TVSideMenu({super.key});

  @override
  State<TVSideMenu> createState() => _TVSideMenuState();
}

class _TVSideMenuState extends State<TVSideMenu> {
  int _selectedIndex = 0;
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 350,
      color: Colors.grey[900],
      child: Column(
        children: [
          // Header
          Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Theme.of(context).primaryColor,
                  Theme.of(context).primaryColor.withValues(alpha: 0.8),
                ],
              ),
            ),
            child: const SafeArea(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.tv, size: 60, color: Colors.white),
                  SizedBox(height: 16),
                  Text(
                    'Hunter TV',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'القنوات التلفزيونية',
                    style: TextStyle(color: Colors.white70, fontSize: 16),
                  ),
                ],
              ),
            ),
          ),

          // Menu Items
          Expanded(
            child: Consumer<ChannelsProvider>(
              builder: (context, provider, child) {
                if (provider.isLoading) {
                  return const Center(
                    child: CircularProgressIndicator(color: Colors.white),
                  );
                }

                if (provider.channelGroups.isEmpty) {
                  return const Center(
                    child: Text(
                      'لا توجد قنوات متاحة',
                      style: TextStyle(color: Colors.white),
                    ),
                  );
                }

                final menuItems = [
                  _MenuItem(
                    icon: Icons.search,
                    title: 'البحث',
                    onTap: () => _showSearchDialog(context),
                  ),
                  _MenuItem(
                    icon: Icons.movie,
                    title: 'أفلام',
                    onTap: () => _showVodSection(context, VodType.movie),
                  ),
                  _MenuItem(
                    icon: Icons.tv,
                    title: 'مسلسلات',
                    onTap: () => _showVodSection(context, VodType.series),
                  ),
                  _MenuItem(
                    icon: Icons.all_inclusive,
                    title: 'جميع القنوات (${provider.totalChannelsCount})',
                    onTap: () => provider.selectGroup(''),
                    isSelected: provider.selectedGroupName.isEmpty,
                  ),
                  ...provider.groupNames.map((groupName) {
                    final group = provider.channelGroups.firstWhere(
                      (g) => g.name == groupName,
                    );

                    return _MenuItem(
                      icon: _getGroupIcon(groupName),
                      title: '$groupName (${group.channels.length})',
                      onTap: () => provider.selectGroup(groupName),
                      isSelected: provider.selectedGroupName == groupName,
                    );
                  }),
                  _MenuItem(
                    icon: Icons.settings,
                    title: 'إعدادات المشغل',
                    onTap: () => _showPlayerSettings(context, provider),
                  ),
                  _MenuItem(
                    icon: Icons.refresh,
                    title: 'تحديث القنوات',
                    onTap: () async {
                      await provider.refreshChannels();
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('تم تحديث القنوات بنجاح'),
                          ),
                        );
                      }
                    },
                  ),
                ];

                return ListView.builder(
                  controller: _scrollController,
                  itemCount: menuItems.length,
                  itemBuilder: (context, index) {
                    final item = menuItems[index];
                    return _TVMenuItem(
                      item: item,
                      isSelected: _selectedIndex == index,
                      autofocus: index == 0,
                      onFocusChanged: (focused) {
                        if (focused) {
                          setState(() {
                            _selectedIndex = index;
                          });
                          _scrollToIndex(index);
                        }
                      },
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _scrollToIndex(int index) {
    final itemHeight = 60.0;
    final targetOffset = index * itemHeight;
    _scrollController.animateTo(
      targetOffset,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  IconData _getGroupIcon(String groupName) {
    final lowerName = groupName.toLowerCase();

    if (lowerName.contains('أفلام') || lowerName.contains('movies')) {
      return Icons.movie;
    } else if (lowerName.contains('رياضة') || lowerName.contains('sport')) {
      return Icons.sports_soccer;
    } else if (lowerName.contains('أطفال') || lowerName.contains('kids')) {
      return Icons.child_care;
    } else if (lowerName.contains('أخبار') || lowerName.contains('news')) {
      return Icons.newspaper;
    } else if (lowerName.contains('دينية') || lowerName.contains('religious')) {
      return Icons.mosque;
    } else if (lowerName.contains('موسيقى') || lowerName.contains('music')) {
      return Icons.music_note;
    } else if (lowerName.contains('وثائقي') ||
        lowerName.contains('documentary')) {
      return Icons.description;
    } else {
      return Icons.tv;
    }
  }

  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('البحث في القنوات'),
            content: TextField(
              autofocus: true,
              decoration: const InputDecoration(
                hintText: 'اكتب اسم القناة...',
                prefixIcon: Icon(Icons.search),
              ),
              onChanged: (query) {
                Provider.of<ChannelsProvider>(
                  context,
                  listen: false,
                ).searchChannels(query);
              },
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إغلاق'),
              ),
            ],
          ),
    );
  }

  void _showPlayerSettings(BuildContext context, ChannelsProvider provider) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('إعدادات المشغل'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('اختر نوع المشغل:'),
                const SizedBox(height: 16),
                RadioListTile<PlayerType>(
                  title: const Text('مشغل داخلي'),
                  subtitle: const Text('يدعم HLS, M3U8, MP4 وصيغ أخرى'),
                  value: PlayerType.internal,
                  groupValue: provider.preferredPlayer,
                  onChanged: (value) {
                    if (value != null) {
                      provider.setPreferredPlayer(value);
                    }
                  },
                ),
                RadioListTile<PlayerType>(
                  title: const Text('مشغل خارجي'),
                  subtitle: const Text('VLC, MX Player وتطبيقات أخرى'),
                  value: PlayerType.external,
                  groupValue: provider.preferredPlayer,
                  onChanged: (value) {
                    if (value != null) {
                      provider.setPreferredPlayer(value);
                    }
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إغلاق'),
              ),
            ],
          ),
    );
  }

  void _showVodSection(BuildContext context, VodType type) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => Scaffold(
              backgroundColor: Colors.black,
              appBar: AppBar(
                title: Text(
                  type == VodType.movie ? 'أفلام' : 'مسلسلات',
                  style: const TextStyle(color: Colors.white),
                ),
                backgroundColor: Colors.black,
                iconTheme: const IconThemeData(color: Colors.white),
              ),
              body: EnhancedVodSection(initialType: type),
            ),
      ),
    );
  }
}

class _MenuItem {
  final IconData icon;
  final String title;
  final VoidCallback onTap;
  final bool isSelected;

  _MenuItem({
    required this.icon,
    required this.title,
    required this.onTap,
    this.isSelected = false,
  });
}

class _TVMenuItem extends StatefulWidget {
  final _MenuItem item;
  final bool isSelected;
  final bool autofocus;
  final ValueChanged<bool> onFocusChanged;

  const _TVMenuItem({
    required this.item,
    required this.isSelected,
    required this.autofocus,
    required this.onFocusChanged,
  });

  @override
  State<_TVMenuItem> createState() => _TVMenuItemState();
}

class _TVMenuItemState extends State<_TVMenuItem> {
  bool _isFocused = false;
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(() {
      final focused = _focusNode.hasFocus;
      setState(() {
        _isFocused = focused;
      });
      widget.onFocusChanged(focused);
    });
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Focus(
      focusNode: _focusNode,
      autofocus: widget.autofocus,
      onKeyEvent: (node, event) {
        if (event is KeyDownEvent) {
          if (event.logicalKey == LogicalKeyboardKey.select ||
              event.logicalKey == LogicalKeyboardKey.enter ||
              event.logicalKey == LogicalKeyboardKey.space) {
            widget.item.onTap();
            return KeyEventResult.handled;
          }
        }
        return KeyEventResult.ignored;
      },
      child: GestureDetector(
        onTap: widget.item.onTap,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          height: 60,
          margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color:
                _isFocused
                    ? Theme.of(context).primaryColor.withValues(alpha: 0.3)
                    : widget.item.isSelected
                    ? Theme.of(context).primaryColor.withValues(alpha: 0.1)
                    : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
            border:
                _isFocused
                    ? Border.all(
                      color: Theme.of(context).primaryColor,
                      width: 2,
                    )
                    : null,
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Icon(
                  widget.item.icon,
                  color:
                      _isFocused || widget.item.isSelected
                          ? Theme.of(context).primaryColor
                          : Colors.white70,
                  size: 24,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    widget.item.title,
                    style: TextStyle(
                      color:
                          _isFocused || widget.item.isSelected
                              ? Theme.of(context).primaryColor
                              : Colors.white,
                      fontSize: 16,
                      fontWeight:
                          widget.item.isSelected
                              ? FontWeight.bold
                              : FontWeight.normal,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                if (_isFocused)
                  Icon(
                    Icons.arrow_forward_ios,
                    color: Theme.of(context).primaryColor,
                    size: 16,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
