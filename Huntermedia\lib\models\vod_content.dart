class VodContent {
  final String name;
  final String url;
  final String? logo;
  final String? groupTitle;
  final String? tvgId;
  final String? tvgName;
  final VodType type;

  VodContent({
    required this.name,
    required this.url,
    this.logo,
    this.groupTitle,
    this.tvgId,
    this.tvgName,
    required this.type,
  });

  factory VodContent.fromM3ULine(String extinfLine, String urlLine) {
    // Parse EXTINF line
    // Example: #EXTINF:-1 group-title="2020 Movies" tvg-name="Movie Name" tvg-logo="logo.jpg" ,Movie Display Name
    
    String name = '';
    String? logo;
    String? groupTitle;
    String? tvgId;
    String? tvgName;

    // Extract content name (after the last comma)
    final commaIndex = extinfLine.lastIndexOf(',');
    if (commaIndex != -1 && commaIndex < extinfLine.length - 1) {
      name = extinfLine.substring(commaIndex + 1).trim();
    }

    // Extract attributes using regex
    final logoMatch = RegExp(r'tvg-logo="([^"]*)"').firstMatch(extinfLine);
    if (logoMatch != null) {
      logo = logoMatch.group(1);
    }

    final groupMatch = RegExp(r'group-title="([^"]*)"').firstMatch(extinfLine);
    if (groupMatch != null) {
      groupTitle = groupMatch.group(1);
    }

    final tvgIdMatch = RegExp(r'tvg-id="([^"]*)"').firstMatch(extinfLine);
    if (tvgIdMatch != null) {
      tvgId = tvgIdMatch.group(1);
    }

    final tvgNameMatch = RegExp(r'tvg-name="([^"]*)"').firstMatch(extinfLine);
    if (tvgNameMatch != null) {
      tvgName = tvgNameMatch.group(1);
    }

    // Determine content type based on group title or URL
    VodType type = VodType.movie;
    if (groupTitle != null) {
      final lowerGroup = groupTitle.toLowerCase();
      if (lowerGroup.contains('series') || lowerGroup.contains('tv') || 
          lowerGroup.contains('show') || urlLine.contains('/series/')) {
        type = VodType.series;
      }
    }

    return VodContent(
      name: name,
      url: urlLine.trim(),
      logo: logo,
      groupTitle: groupTitle ?? 'أفلام',
      tvgId: tvgId,
      tvgName: tvgName,
      type: type,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'url': url,
      'logo': logo,
      'groupTitle': groupTitle,
      'tvgId': tvgId,
      'tvgName': tvgName,
      'type': type.toString(),
    };
  }

  factory VodContent.fromJson(Map<String, dynamic> json) {
    return VodContent(
      name: json['name'] ?? '',
      url: json['url'] ?? '',
      logo: json['logo'],
      groupTitle: json['groupTitle'],
      tvgId: json['tvgId'],
      tvgName: json['tvgName'],
      type: VodType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => VodType.movie,
      ),
    );
  }

  // Helper method to get clean poster URL
  String? get posterUrl {
    if (logo == null || logo!.isEmpty) return null;
    
    // Clean up the URL - remove escape characters
    String cleanUrl = logo!.replaceAll(r'\_', '_');
    
    // Ensure it's a valid image URL
    if (cleanUrl.startsWith('http') && 
        (cleanUrl.contains('.jpg') || cleanUrl.contains('.png') || 
         cleanUrl.contains('.jpeg') || cleanUrl.contains('.webp'))) {
      return cleanUrl;
    }
    
    return null;
  }

  // Helper method to get year from name
  String? get year {
    final yearMatch = RegExp(r'\((\d{4})\)').firstMatch(name);
    return yearMatch?.group(1);
  }

  // Helper method to get clean title without year
  String get cleanTitle {
    return name.replaceAll(RegExp(r'\s*\(\d{4}\)\s*'), '').trim();
  }
}

enum VodType {
  movie,
  series,
}

class VodGroup {
  final String name;
  final List<VodContent> content;
  final VodType type;

  VodGroup({
    required this.name,
    required this.content,
    required this.type,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'content': content.map((item) => item.toJson()).toList(),
      'type': type.toString(),
    };
  }

  factory VodGroup.fromJson(Map<String, dynamic> json) {
    return VodGroup(
      name: json['name'] ?? '',
      content: (json['content'] as List<dynamic>?)
              ?.map((itemJson) => VodContent.fromJson(itemJson))
              .toList() ??
          [],
      type: VodType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => VodType.movie,
      ),
    );
  }
}
