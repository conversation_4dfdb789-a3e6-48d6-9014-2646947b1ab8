{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\myapp\\Huntermedia\\Huntermedia\\Huntermedia\\android\\app\\.cxx\\RelWithDebInfo\\6w1t6967\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\myapp\\Huntermedia\\Huntermedia\\Huntermedia\\android\\app\\.cxx\\RelWithDebInfo\\6w1t6967\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}