<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Internet permission for downloading M3U files -->
    <uses-permission android:name="android.permission.INTERNET" />
    <!-- Permission to query other apps for launching external players -->
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />

    <!-- Android TV features -->
    <uses-feature android:name="android.software.leanback"
                  android:required="false" />
    <uses-feature android:name="android.hardware.touchscreen"
                  android:required="false" />
    <application
        android:label="huntertv"
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher"
        android:banner="@mipmap/ic_launcher">

        <!-- Android TV support -->
        <meta-data android:name="android.software.leanback"
                   android:value="true" />
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:taskAffinity=""
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
                <category android:name="android.intent.category.LEANBACK_LAUNCHER"/>
            </intent-filter>
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
    </application>
    <!-- Required to query activities that can process text, see:
         https://developer.android.com/training/package-visibility and
         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin. -->
    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT"/>
            <data android:mimeType="text/plain"/>
        </intent>
        <!-- Query for video players -->
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:mimeType="video/*" />
        </intent>
        <!-- Query for streaming apps -->
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="http" />
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="https" />
        </intent>
        <!-- VLC Player -->
        <package android:name="org.videolan.vlc" />
        <!-- MX Player -->
        <package android:name="com.mxtech.videoplayer.ad" />
        <package android:name="com.mxtech.videoplayer.pro" />
    </queries>
</manifest>
