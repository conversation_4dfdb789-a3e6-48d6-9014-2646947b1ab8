import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../providers/channels_provider.dart';
import '../models/channel.dart';
import '../widgets/side_menu.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<ChannelsProvider>(context, listen: false).loadChannels();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title:
            _isSearching
                ? TextField(
                  controller: _searchController,
                  autofocus: true,
                  style: const TextStyle(color: Colors.white),
                  decoration: const InputDecoration(
                    hintText: 'البحث في القنوات...',
                    hintStyle: TextStyle(color: Colors.white70),
                    border: InputBorder.none,
                  ),
                  onChanged: (query) {
                    Provider.of<ChannelsProvider>(
                      context,
                      listen: false,
                    ).searchChannels(query);
                  },
                )
                : const Text('Hunter TV'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: Icon(_isSearching ? Icons.close : Icons.search),
            onPressed: () {
              setState(() {
                _isSearching = !_isSearching;
                if (!_isSearching) {
                  _searchController.clear();
                  Provider.of<ChannelsProvider>(
                    context,
                    listen: false,
                  ).searchChannels('');
                }
              });
            },
          ),
          Consumer<ChannelsProvider>(
            builder: (context, provider, child) {
              return IconButton(
                icon: const Icon(Icons.refresh),
                onPressed:
                    provider.isLoading
                        ? null
                        : () async {
                          await provider.refreshChannels();
                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('تم تحديث القنوات بنجاح'),
                              ),
                            );
                          }
                        },
              );
            },
          ),
        ],
      ),
      drawer: const SideMenu(),
      body: Consumer<ChannelsProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('جاري تحميل القنوات...'),
                ],
              ),
            );
          }

          if (provider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 64, color: Colors.red),
                  const SizedBox(height: 16),
                  Text(
                    'خطأ في تحميل القنوات',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    provider.error!,
                    textAlign: TextAlign.center,
                    style: const TextStyle(color: Colors.grey),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      provider.clearError();
                      provider.loadChannels(forceRefresh: true);
                    },
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          // Determine which channels to show
          List<dynamic> channelsToShow;
          String title;

          if (provider.searchQuery.isNotEmpty) {
            channelsToShow = provider.searchResults;
            title = 'نتائج البحث (${provider.searchResults.length})';
          } else if (provider.selectedGroupName.isEmpty) {
            channelsToShow =
                provider.channelGroups
                    .expand((group) => group.channels)
                    .toList();
            title = 'جميع القنوات (${provider.totalChannelsCount})';
          } else {
            channelsToShow = provider.selectedGroupChannels;
            title =
                '${provider.selectedGroupName} (${provider.selectedGroupChannels.length})';
          }

          if (channelsToShow.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.tv_off, size: 64, color: Colors.grey),
                  const SizedBox(height: 16),
                  Text(
                    provider.searchQuery.isNotEmpty
                        ? 'لا توجد نتائج للبحث'
                        : 'لا توجد قنوات متاحة',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // Title bar
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                color: Colors.grey[100],
                child: Text(
                  title,
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
              ),

              // Channels Grid
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(8),
                  child: GridView.builder(
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2, // عمودين للهاتف
                          childAspectRatio:
                              1.3, // زيادة النسبة لتوفير مساحة أكبر
                          crossAxisSpacing: 12,
                          mainAxisSpacing: 12,
                        ),
                    itemCount: channelsToShow.length,
                    itemBuilder: (context, index) {
                      final channel = channelsToShow[index];
                      return _GridChannelCard(
                        channel: channel,
                        onTap: () => provider.playChannel(channel, context),
                      );
                    },
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}

// بطاقة القناة للعرض في الشبكة
class _GridChannelCard extends StatelessWidget {
  final Channel channel;
  final VoidCallback onTap;

  const _GridChannelCard({required this.channel, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Column(
          children: [
            // صورة القناة
            Expanded(
              flex: 2, // تقليل المساحة لتجنب الفيض
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(8),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.grey[200],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child:
                        channel.logo != null && channel.logo!.isNotEmpty
                            ? CachedNetworkImage(
                              imageUrl: channel.logo!,
                              fit: BoxFit.contain, // تغيير إلى contain
                              width: double.infinity,
                              height: double.infinity,
                              placeholder:
                                  (context, url) => Container(
                                    color: Colors.grey[100],
                                    child: const Center(
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                      ),
                                    ),
                                  ),
                              errorWidget:
                                  (context, url, error) => Container(
                                    color: Colors.grey[100],
                                    child: const Center(
                                      child: Icon(
                                        Icons.tv,
                                        color: Colors.grey,
                                        size: 30,
                                      ),
                                    ),
                                  ),
                            )
                            : Container(
                              color: Colors.grey[100],
                              child: const Center(
                                child: Icon(
                                  Icons.tv,
                                  color: Colors.grey,
                                  size: 30,
                                ),
                              ),
                            ),
                  ),
                ),
              ),
            ),

            // معلومات القناة
            Expanded(
              flex: 1, // تقليل المساحة لتجنب الفيض
              child: Padding(
                padding: const EdgeInsets.all(8),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min, // حماية من الفيض
                  children: [
                    Text(
                      channel.name,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                    ),

                    if (channel.groupTitle != null &&
                        channel.groupTitle!.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: Theme.of(
                              context,
                            ).primaryColor.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            channel.groupTitle!,
                            style: TextStyle(
                              fontSize: 10,
                              color: Theme.of(context).primaryColor,
                              fontWeight: FontWeight.w500,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
