import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../models/vod_content.dart';
import '../services/vod_service.dart';
import '../models/channel.dart';
import '../screens/video_player_screen.dart';

class VodSection extends StatefulWidget {
  const VodSection({super.key});

  @override
  State<VodSection> createState() => _VodSectionState();
}

class _VodSectionState extends State<VodSection> {
  List<VodGroup> _vodGroups = [];
  bool _isLoading = false;
  String? _error;
  VodType _selectedType = VodType.movie;

  @override
  void initState() {
    super.initState();
    _loadVodContent();
  }

  Future<void> _loadVodContent() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final groups = await VodService.fetchVodContent();
      setState(() {
        _vodGroups = groups;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  List<VodContent> get _currentContent {
    return _vodGroups
        .where((group) => group.type == _selectedType)
        .expand((group) => group.content)
        .where((content) => content.type == _selectedType)
        .toList();
  }

  void _playContent(VodContent content) {
    // Convert VodContent to Channel for compatibility with existing video player
    final channel = Channel(
      name: content.name,
      url: content.url,
      logo: content.logo,
      groupTitle: content.groupTitle,
      tvgId: content.tvgId,
      tvgName: content.tvgName,
    );

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VideoPlayerScreen(channel: channel),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with type selector
        Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'محتوى الفيديو حسب الطلب',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  _buildTypeButton(VodType.movie, 'أفلام', Icons.movie),
                  const SizedBox(width: 12),
                  _buildTypeButton(VodType.series, 'مسلسلات', Icons.tv),
                ],
              ),
            ],
          ),
        ),

        // Content
        Expanded(
          child: _buildContent(),
        ),
      ],
    );
  }

  Widget _buildTypeButton(VodType type, String label, IconData icon) {
    final isSelected = _selectedType == type;
    return ElevatedButton.icon(
      onPressed: () {
        setState(() {
          _selectedType = type;
        });
      },
      icon: Icon(icon),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: isSelected
            ? Theme.of(context).primaryColor
            : Colors.grey[700],
        foregroundColor: Colors.white,
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: Colors.white),
            SizedBox(height: 16),
            Text(
              'جاري تحميل المحتوى...',
              style: TextStyle(color: Colors.white, fontSize: 16),
            ),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 64,
            ),
            const SizedBox(height: 16),
            Text(
              'خطأ في تحميل المحتوى',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: const TextStyle(color: Colors.white70),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadVodContent,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_currentContent.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _selectedType == VodType.movie ? Icons.movie : Icons.tv,
              color: Colors.white54,
              size: 64,
            ),
            const SizedBox(height: 16),
            Text(
              _selectedType == VodType.movie
                  ? 'لا توجد أفلام متاحة'
                  : 'لا توجد مسلسلات متاحة',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
              ),
            ),
          ],
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: _selectedType == VodType.movie ? 4 : 3,
          childAspectRatio: _selectedType == VodType.movie ? 0.7 : 0.8,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
        ),
        itemCount: _currentContent.length,
        itemBuilder: (context, index) {
          final content = _currentContent[index];
          return _VodCard(
            content: content,
            onTap: () => _playContent(content),
          );
        },
      ),
    );
  }
}

class _VodCard extends StatefulWidget {
  final VodContent content;
  final VoidCallback onTap;

  const _VodCard({
    required this.content,
    required this.onTap,
  });

  @override
  State<_VodCard> createState() => _VodCardState();
}

class _VodCardState extends State<_VodCard> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: GestureDetector(
        onTap: widget.onTap,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          transform: Matrix4.identity()
            ..scale(_isHovered ? 1.05 : 1.0),
          child: Card(
            elevation: _isHovered ? 12 : 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            clipBehavior: Clip.antiAlias,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Poster
                Expanded(
                  flex: 3,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.grey[900],
                    ),
                    child: widget.content.posterUrl != null
                        ? CachedNetworkImage(
                            imageUrl: widget.content.posterUrl!,
                            fit: BoxFit.cover,
                            placeholder: (context, url) => Container(
                              color: Colors.grey[800],
                              child: const Center(
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: Colors.white54,
                                ),
                              ),
                            ),
                            errorWidget: (context, url, error) => Container(
                              color: Colors.grey[800],
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    widget.content.type == VodType.movie
                                        ? Icons.movie
                                        : Icons.tv,
                                    color: Colors.white54,
                                    size: 40,
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    widget.content.type == VodType.movie
                                        ? 'فيلم'
                                        : 'مسلسل',
                                    style: const TextStyle(
                                      color: Colors.white54,
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          )
                        : Container(
                            color: Colors.grey[800],
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  widget.content.type == VodType.movie
                                      ? Icons.movie
                                      : Icons.tv,
                                  color: Colors.white54,
                                  size: 40,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  widget.content.type == VodType.movie
                                      ? 'فيلم'
                                      : 'مسلسل',
                                  style: const TextStyle(
                                    color: Colors.white54,
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ),
                  ),
                ),

                // Info
                Expanded(
                  flex: 1,
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    color: Colors.grey[900],
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          widget.content.cleanTitle,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (widget.content.year != null) ...[
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Icon(
                                Icons.calendar_today,
                                size: 10,
                                color: Colors.white70,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                widget.content.year!,
                                style: const TextStyle(
                                  color: Colors.white70,
                                  fontSize: 10,
                                ),
                              ),
                              const Spacer(),
                              Icon(
                                widget.content.type == VodType.movie
                                    ? Icons.movie
                                    : Icons.tv,
                                size: 10,
                                color: Colors.white70,
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
