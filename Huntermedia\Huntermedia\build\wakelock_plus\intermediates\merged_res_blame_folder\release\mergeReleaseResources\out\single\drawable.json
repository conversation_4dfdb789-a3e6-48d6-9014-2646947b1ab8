[{"merged": "dev.fluttercommunity.plus.wakelock.wakelock_plus-release-26:/drawable/notification_tile_bg.xml", "source": "dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-8:/drawable/notification_tile_bg.xml"}, {"merged": "dev.fluttercommunity.plus.wakelock.wakelock_plus-release-26:/drawable/notification_bg_low.xml", "source": "dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-8:/drawable/notification_bg_low.xml"}, {"merged": "dev.fluttercommunity.plus.wakelock.wakelock_plus-release-26:/drawable/notification_bg.xml", "source": "dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-8:/drawable/notification_bg.xml"}, {"merged": "dev.fluttercommunity.plus.wakelock.wakelock_plus-release-26:/drawable/notification_icon_background.xml", "source": "dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-8:/drawable/notification_icon_background.xml"}]