import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/channel.dart';
import '../models/channel_group.dart';
import '../services/m3u_service.dart';
import '../services/storage_service.dart';
import '../screens/video_player_screen.dart';

enum PlayerType { internal, external }

class ChannelsProvider with ChangeNotifier {
  List<ChannelGroup> _channelGroups = [];
  List<Channel> _searchResults = [];
  bool _isLoading = false;
  String? _error;
  String _selectedGroupName = '';
  String _searchQuery = '';
  PlayerType _preferredPlayer = PlayerType.internal;

  List<ChannelGroup> get channelGroups => _channelGroups;
  List<Channel> get searchResults => _searchResults;
  bool get isLoading => _isLoading;
  String? get error => _error;
  String get selectedGroupName => _selectedGroupName;
  String get searchQuery => _searchQuery;
  PlayerType get preferredPlayer => _preferredPlayer;

  List<String> get groupNames =>
      _channelGroups.map((group) => group.name).toList();

  List<Channel> get selectedGroupChannels {
    if (_selectedGroupName.isEmpty && _channelGroups.isNotEmpty) {
      return _channelGroups.first.channels;
    }
    final group = _channelGroups.firstWhere(
      (group) => group.name == _selectedGroupName,
      orElse:
          () =>
              _channelGroups.isNotEmpty
                  ? _channelGroups.first
                  : ChannelGroup(name: '', channels: []),
    );
    return group.channels;
  }

  Future<void> loadChannels({bool forceRefresh = false}) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    // Load player preference
    await _loadPlayerPreference();

    try {
      // Check if we should load from cache or fetch new data
      if (!forceRefresh) {
        final cachedChannels = await StorageService.loadChannels();
        final shouldUpdate = await StorageService.shouldUpdate();

        if (cachedChannels != null && !shouldUpdate) {
          _channelGroups = cachedChannels;
          if (_selectedGroupName.isEmpty && _channelGroups.isNotEmpty) {
            _selectedGroupName = _channelGroups.first.name;
          }
          _isLoading = false;
          notifyListeners();
          return;
        }
      }

      // Fetch new data
      final groups = await M3UService.fetchChannels();
      _channelGroups = groups;

      // Save to cache
      await StorageService.saveChannels(groups);

      // Set default selected group
      if (_selectedGroupName.isEmpty && _channelGroups.isNotEmpty) {
        _selectedGroupName = _channelGroups.first.name;
      }
    } catch (e) {
      _error = e.toString();

      // Try to load from cache as fallback
      final cachedChannels = await StorageService.loadChannels();
      if (cachedChannels != null) {
        _channelGroups = cachedChannels;
        if (_selectedGroupName.isEmpty && _channelGroups.isNotEmpty) {
          _selectedGroupName = _channelGroups.first.name;
        }
      }
    }

    _isLoading = false;
    notifyListeners();
  }

  void selectGroup(String groupName) {
    _selectedGroupName = groupName;
    _searchQuery = '';
    _searchResults.clear();
    notifyListeners();
  }

  void searchChannels(String query) {
    _searchQuery = query;

    if (query.isEmpty) {
      _searchResults.clear();
    } else {
      _searchResults =
          _channelGroups
              .expand((group) => group.channels)
              .where(
                (channel) =>
                    channel.name.toLowerCase().contains(query.toLowerCase()) ||
                    (channel.groupTitle?.toLowerCase().contains(
                          query.toLowerCase(),
                        ) ??
                        false),
              )
              .toList();
    }

    notifyListeners();
  }

  Future<void> playChannel(Channel channel, BuildContext context) async {
    try {
      if (_preferredPlayer == PlayerType.internal) {
        // Use internal player
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => VideoPlayerScreen(channel: channel),
          ),
        );
      } else {
        // Use external player
        final uri = Uri.parse(channel.url);

        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
        } else {
          throw Exception('لا يمكن فتح الرابط');
        }
      }
    } catch (e) {
      _error = 'خطأ في تشغيل القناة: $e';
      notifyListeners();
    }
  }

  void setPreferredPlayer(PlayerType playerType) {
    _preferredPlayer = playerType;
    notifyListeners();
    _savePlayerPreference();
  }

  Future<void> _savePlayerPreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('preferred_player', _preferredPlayer.name);
    } catch (e) {
      print('Error saving player preference: $e');
    }
  }

  Future<void> _loadPlayerPreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final playerName = prefs.getString('preferred_player');
      if (playerName != null) {
        _preferredPlayer = PlayerType.values.firstWhere(
          (type) => type.name == playerName,
          orElse: () => PlayerType.internal,
        );
      }
    } catch (e) {
      print('Error loading player preference: $e');
    }
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  Future<void> refreshChannels() async {
    await loadChannels(forceRefresh: true);
  }

  int get totalChannelsCount {
    return _channelGroups.fold(0, (sum, group) => sum + group.channels.length);
  }
}
