import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/vod_content.dart';
import '../models/channel.dart';
import '../services/vod_service.dart';
import 'channels_provider.dart'; // For PlayerType enum
import '../screens/video_player_screen.dart';

// Sorting options for VOD content
enum VodSortOption {
  nameAsc,
  nameDesc,
  yearAsc,
  yearDesc,
  groupAsc,
  groupDesc,
  dateAdded,
}

// Filter options for VOD content
class VodFilter {
  final String? genre;
  final int? startYear;
  final int? endYear;
  final String? groupName;
  final bool favoritesOnly;

  VodFilter({
    this.genre,
    this.startYear,
    this.endYear,
    this.groupName,
    this.favoritesOnly = false,
  });

  VodFilter copyWith({
    String? genre,
    int? startYear,
    int? endYear,
    String? groupName,
    bool? favoritesOnly,
  }) {
    return VodFilter(
      genre: genre ?? this.genre,
      startYear: startYear ?? this.startYear,
      endYear: endYear ?? this.endYear,
      groupName: groupName ?? this.groupName,
      favoritesOnly: favoritesOnly ?? this.favoritesOnly,
    );
  }

  bool get hasActiveFilters =>
      genre != null ||
      startYear != null ||
      endYear != null ||
      groupName != null ||
      favoritesOnly;
}

class VodProvider with ChangeNotifier {
  List<VodGroup> _vodGroups = [];
  List<VodContent> _searchResults = [];
  List<VodContent> _filteredContent = [];
  Set<String> _favoriteIds = {};
  bool _isLoading = false;
  String? _error;
  String _selectedGroupName = '';
  String _searchQuery = '';
  VodType _selectedType = VodType.movie;
  PlayerType _preferredPlayer = PlayerType.internal;
  VodSortOption _sortOption = VodSortOption.nameAsc;
  VodFilter _filter = VodFilter();

  List<VodGroup> get vodGroups => _vodGroups;
  List<VodContent> get searchResults => _searchResults;
  List<VodContent> get filteredContent => _filteredContent;
  Set<String> get favoriteIds => _favoriteIds;
  bool get isLoading => _isLoading;
  String? get error => _error;
  String get selectedGroupName => _selectedGroupName;
  String get searchQuery => _searchQuery;
  VodType get selectedType => _selectedType;
  PlayerType get preferredPlayer => _preferredPlayer;
  VodSortOption get sortOption => _sortOption;
  VodFilter get filter => _filter;

  List<String> get groupNames =>
      _vodGroups
          .where((group) => group.type == _selectedType)
          .map((group) => group.name)
          .toList();

  List<VodContent> get selectedGroupContent {
    if (_searchQuery.isNotEmpty) {
      return _searchResults;
    }

    if (_filter.hasActiveFilters || _filteredContent.isNotEmpty) {
      return _filteredContent;
    }

    if (_selectedGroupName.isEmpty) {
      // Return all content of selected type
      return _vodGroups
          .where((group) => group.type == _selectedType)
          .expand((group) => group.content)
          .where((content) => content.type == _selectedType)
          .toList();
    }

    final group = _vodGroups.firstWhere(
      (group) =>
          group.name == _selectedGroupName && group.type == _selectedType,
      orElse: () => VodGroup(name: '', content: [], type: _selectedType),
    );
    return group.content;
  }

  List<VodContent> get allMovies => VodService.getMoviesFromGroups(_vodGroups);
  List<VodContent> get allSeries => VodService.getSeriesFromGroups(_vodGroups);
  List<VodContent> get favoriteMovies =>
      allMovies.where((movie) => _favoriteIds.contains(movie.url)).toList();
  List<VodContent> get favoriteSeries =>
      allSeries.where((series) => _favoriteIds.contains(series.url)).toList();

  int get totalMoviesCount => allMovies.length;
  int get totalSeriesCount => allSeries.length;
  int get totalContentCount => totalMoviesCount + totalSeriesCount;
  int get favoritesCount => _favoriteIds.length;

  // Get available years for filtering
  List<int> get availableYears {
    final years = <int>{};
    for (final group in _vodGroups) {
      for (final content in group.content) {
        if (content.year != null) {
          final year = int.tryParse(content.year!);
          if (year != null) {
            years.add(year);
          }
        }
      }
    }
    final sortedYears = years.toList()..sort();
    return sortedYears.reversed.toList(); // Most recent first
  }

  // Get available genres/groups for filtering
  List<String> get availableGenres {
    final genres = <String>{};
    for (final group in _vodGroups) {
      if (group.type == _selectedType) {
        genres.add(group.name);
      }
    }
    return genres.toList()..sort();
  }

  Future<void> loadVodContent({bool forceRefresh = false}) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Load favorites first
      await _loadFavorites();

      // Check if we should load from cache or fetch new data
      if (!forceRefresh) {
        final cachedContent = await _loadCachedVodContent();
        final shouldUpdate = await _shouldUpdateVodContent();

        if (cachedContent != null && !shouldUpdate) {
          _vodGroups = cachedContent;
          _applyCurrentFilters();
          _isLoading = false;
          notifyListeners();
          return;
        }
      }

      // Fetch new data
      final groups = await VodService.fetchVodContent();
      _vodGroups = groups;
      _applyCurrentFilters();

      // Save to cache
      await _saveVodContentToCache(groups);
    } catch (e) {
      _error = e.toString();

      // Try to load from cache as fallback
      final cachedContent = await _loadCachedVodContent();
      if (cachedContent != null) {
        _vodGroups = cachedContent;
        _applyCurrentFilters();
      }
    }

    _isLoading = false;
    notifyListeners();
  }

  void selectType(VodType type) {
    _selectedType = type;
    _selectedGroupName = '';
    _searchQuery = '';
    _searchResults.clear();
    _filter = VodFilter();
    _applyCurrentFilters();
    notifyListeners();
  }

  void selectGroup(String groupName) {
    _selectedGroupName = groupName;
    _searchQuery = '';
    _searchResults.clear();
    _filter = VodFilter();
    _applyCurrentFilters();
    notifyListeners();
  }

  void searchContent(String query) {
    _searchQuery = query;

    if (query.isEmpty) {
      _searchResults.clear();
    } else {
      _searchResults =
          VodService.searchContent(
            _vodGroups,
            query,
          ).where((content) => content.type == _selectedType).toList();

      // Apply sorting to search results
      _sortContent(_searchResults);
    }

    notifyListeners();
  }

  void setSortOption(VodSortOption option) {
    _sortOption = option;
    _applyCurrentFilters();
    notifyListeners();
  }

  void setFilter(VodFilter filter) {
    _filter = filter;
    _applyCurrentFilters();
    notifyListeners();
  }

  void clearFilters() {
    _filter = VodFilter();
    _applyCurrentFilters();
    notifyListeners();
  }

  void _applyCurrentFilters() {
    List<VodContent> content;

    if (_selectedGroupName.isEmpty) {
      // Get all content of selected type
      content =
          _vodGroups
              .where((group) => group.type == _selectedType)
              .expand((group) => group.content)
              .where((content) => content.type == _selectedType)
              .toList();
    } else {
      // Get content from selected group
      final group = _vodGroups.firstWhere(
        (group) =>
            group.name == _selectedGroupName && group.type == _selectedType,
        orElse: () => VodGroup(name: '', content: [], type: _selectedType),
      );
      content = group.content;
    }

    // Apply filters
    if (_filter.hasActiveFilters) {
      content =
          content.where((item) {
            // Genre filter
            if (_filter.genre != null && item.groupTitle != _filter.genre) {
              return false;
            }

            // Year range filter
            if (_filter.startYear != null || _filter.endYear != null) {
              final year = item.year != null ? int.tryParse(item.year!) : null;
              if (year != null) {
                if (_filter.startYear != null && year < _filter.startYear!) {
                  return false;
                }
                if (_filter.endYear != null && year > _filter.endYear!) {
                  return false;
                }
              } else if (_filter.startYear != null || _filter.endYear != null) {
                return false; // Exclude items without year when year filter is active
              }
            }

            // Favorites filter
            if (_filter.favoritesOnly && !_favoriteIds.contains(item.url)) {
              return false;
            }

            return true;
          }).toList();
    }

    // Apply sorting
    _sortContent(content);
    _filteredContent = content;
  }

  void _sortContent(List<VodContent> content) {
    switch (_sortOption) {
      case VodSortOption.nameAsc:
        content.sort((a, b) => a.cleanTitle.compareTo(b.cleanTitle));
        break;
      case VodSortOption.nameDesc:
        content.sort((a, b) => b.cleanTitle.compareTo(a.cleanTitle));
        break;
      case VodSortOption.yearAsc:
        content.sort((a, b) {
          final yearA = a.year != null ? int.tryParse(a.year!) ?? 0 : 0;
          final yearB = b.year != null ? int.tryParse(b.year!) ?? 0 : 0;
          return yearA.compareTo(yearB);
        });
        break;
      case VodSortOption.yearDesc:
        content.sort((a, b) {
          final yearA = a.year != null ? int.tryParse(a.year!) ?? 0 : 0;
          final yearB = b.year != null ? int.tryParse(b.year!) ?? 0 : 0;
          return yearB.compareTo(yearA);
        });
        break;
      case VodSortOption.groupAsc:
        content.sort(
          (a, b) => (a.groupTitle ?? '').compareTo(b.groupTitle ?? ''),
        );
        break;
      case VodSortOption.groupDesc:
        content.sort(
          (a, b) => (b.groupTitle ?? '').compareTo(a.groupTitle ?? ''),
        );
        break;
      case VodSortOption.dateAdded:
        // Keep original order (newest first in M3U)
        break;
    }
  }

  // Favorites management
  bool isFavorite(VodContent content) {
    return _favoriteIds.contains(content.url);
  }

  void toggleFavorite(VodContent content) {
    if (_favoriteIds.contains(content.url)) {
      _favoriteIds.remove(content.url);
    } else {
      _favoriteIds.add(content.url);
    }
    _saveFavorites();
    _applyCurrentFilters(); // Refresh filtered content if favorites filter is active
    notifyListeners();
  }

  void addToFavorites(VodContent content) {
    _favoriteIds.add(content.url);
    _saveFavorites();
    _applyCurrentFilters();
    notifyListeners();
  }

  void removeFromFavorites(VodContent content) {
    _favoriteIds.remove(content.url);
    _saveFavorites();
    _applyCurrentFilters();
    notifyListeners();
  }

  Future<void> refreshVodContent() async {
    await loadVodContent(forceRefresh: true);
  }

  void setPreferredPlayer(PlayerType playerType) {
    _preferredPlayer = playerType;
    _savePlayerPreference();
    notifyListeners();
  }

  Future<void> playContent(VodContent content, BuildContext context) async {
    // Show player selection dialog
    final selectedPlayer = await _showPlayerSelectionDialog(context);

    if (selectedPlayer == null) {
      return; // User cancelled
    }

    try {
      if (selectedPlayer == PlayerType.internal) {
        // Use internal player - create a Channel object from VodContent
        final channel = Channel(
          name: content.name,
          url: content.url,
          logo: content.logo,
          groupTitle: content.groupTitle,
          tvgId: content.tvgId,
          tvgName: content.tvgName,
        );

        if (context.mounted) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => VideoPlayerScreen(channel: channel),
            ),
          );
        }
      } else {
        // Use external player
        final uri = Uri.parse(content.url);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
        } else {
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('لا يمكن فتح المشغل الخارجي'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تشغيل المحتوى: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<PlayerType?> _showPlayerSelectionDialog(BuildContext context) async {
    return showDialog<PlayerType>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('اختر المشغل'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('كيف تريد تشغيل هذا المحتوى؟'),
                const SizedBox(height: 16),

                // Internal player option
                ListTile(
                  leading: const Icon(
                    Icons.play_circle_filled,
                    color: Colors.blue,
                  ),
                  title: const Text('المشغل الداخلي'),
                  subtitle: const Text('يدعم HLS, M3U8, MP4 وصيغ أخرى'),
                  onTap: () => Navigator.of(context).pop(PlayerType.internal),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                    side: BorderSide(color: Colors.grey.shade300),
                  ),
                ),

                const SizedBox(height: 8),

                // External player option
                ListTile(
                  leading: const Icon(Icons.open_in_new, color: Colors.green),
                  title: const Text('مشغل خارجي'),
                  subtitle: const Text('VLC, MX Player وتطبيقات أخرى'),
                  onTap: () => Navigator.of(context).pop(PlayerType.external),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                    side: BorderSide(color: Colors.grey.shade300),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
            ],
          ),
    );
  }

  // Cache management methods
  Future<List<VodGroup>?> _loadCachedVodContent() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString('cached_vod_content');

      if (jsonString != null) {
        final List<dynamic> jsonList = jsonDecode(jsonString);
        return jsonList.map((json) => VodGroup.fromJson(json)).toList();
      }
    } catch (e) {
      print('Error loading cached VOD content: $e');
    }
    return null;
  }

  Future<void> _saveVodContentToCache(List<VodGroup> groups) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = jsonEncode(
        groups.map((group) => group.toJson()).toList(),
      );
      await prefs.setString('cached_vod_content', jsonString);
      await prefs.setInt(
        'vod_last_update',
        DateTime.now().millisecondsSinceEpoch,
      );
    } catch (e) {
      print('Error saving VOD content to cache: $e');
    }
  }

  Future<bool> _shouldUpdateVodContent() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestamp = prefs.getInt('vod_last_update');
      if (timestamp != null) {
        final lastUpdate = DateTime.fromMillisecondsSinceEpoch(timestamp);
        final now = DateTime.now();
        final difference = now.difference(lastUpdate);
        return difference.inHours >= 24; // Update every 24 hours
      }
    } catch (e) {
      print('Error checking VOD update time: $e');
    }
    return true;
  }

  // Favorites persistence
  Future<void> _loadFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritesJson = prefs.getStringList('vod_favorites');
      if (favoritesJson != null) {
        _favoriteIds = favoritesJson.toSet();
      }
    } catch (e) {
      print('Error loading favorites: $e');
    }
  }

  Future<void> _saveFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList('vod_favorites', _favoriteIds.toList());
    } catch (e) {
      print('Error saving favorites: $e');
    }
  }

  Future<void> _savePlayerPreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('preferred_player', _preferredPlayer.toString());
    } catch (e) {
      print('Error saving player preference: $e');
    }
  }

  Future<void> _loadPlayerPreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final playerString = prefs.getString('preferred_player');
      if (playerString != null) {
        _preferredPlayer = PlayerType.values.firstWhere(
          (type) => type.toString() == playerString,
          orElse: () => PlayerType.internal,
        );
      }
    } catch (e) {
      print('Error loading player preference: $e');
    }
  }

  // Helper methods for UI
  String getSortOptionLabel(VodSortOption option) {
    switch (option) {
      case VodSortOption.nameAsc:
        return 'الاسم (أ-ي)';
      case VodSortOption.nameDesc:
        return 'الاسم (ي-أ)';
      case VodSortOption.yearAsc:
        return 'السنة (الأقدم أولاً)';
      case VodSortOption.yearDesc:
        return 'السنة (الأحدث أولاً)';
      case VodSortOption.groupAsc:
        return 'التصنيف (أ-ي)';
      case VodSortOption.groupDesc:
        return 'التصنيف (ي-أ)';
      case VodSortOption.dateAdded:
        return 'تاريخ الإضافة';
    }
  }
}
