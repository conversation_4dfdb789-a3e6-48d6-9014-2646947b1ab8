import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../providers/channels_provider.dart';
import '../widgets/tv_side_menu.dart';
import '../widgets/side_menu.dart';
import '../widgets/tv_grid_card.dart';
import '../widgets/responsive_channel_card.dart';

class ResponsiveHomeScreen extends StatefulWidget {
  const ResponsiveHomeScreen({super.key});

  @override
  State<ResponsiveHomeScreen> createState() => _ResponsiveHomeScreenState();
}

class _ResponsiveHomeScreenState extends State<ResponsiveHomeScreen> {
  bool _isMenuVisible = false;
  bool _isSearching = false;
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _menuFocusNode = FocusNode();
  final FocusNode _contentFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<ChannelsProvider>(context, listen: false).loadChannels();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _menuFocusNode.dispose();
    _contentFocusNode.dispose();
    super.dispose();
  }

  // Platform detection methods
  bool _isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < 600;
  }

  bool _isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= 600 && width < 1200;
  }

  bool _isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= 1200;
  }

  // Grid configuration based on platform
  int _getCrossAxisCount(BuildContext context) {
    if (_isMobile(context)) {
      return 2; // 2 columns on mobile
    } else if (_isTablet(context)) {
      return 3; // 3 columns on tablet
    } else {
      return 4; // 4 columns on desktop/TV
    }
  }

  double _getChildAspectRatio(BuildContext context) {
    if (_isMobile(context)) {
      return 1.3; // Mobile aspect ratio
    } else if (_isTablet(context)) {
      return 1.4; // Tablet aspect ratio
    } else {
      return 1.5; // Desktop/TV aspect ratio
    }
  }

  double _getSpacing(BuildContext context) {
    if (_isMobile(context)) {
      return 8.0; // Smaller spacing on mobile
    } else if (_isTablet(context)) {
      return 12.0; // Medium spacing on tablet
    } else {
      return 16.0; // Larger spacing on desktop/TV
    }
  }

  EdgeInsets _getPadding(BuildContext context) {
    if (_isMobile(context)) {
      return const EdgeInsets.all(8.0); // Smaller padding on mobile
    } else if (_isTablet(context)) {
      return const EdgeInsets.all(12.0); // Medium padding on tablet
    } else {
      return const EdgeInsets.all(16.0); // Larger padding on desktop/TV
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDesktopOrTV = _isDesktop(context);

    if (isDesktopOrTV) {
      return _buildDesktopTVLayout();
    } else {
      return _buildMobileTabletLayout();
    }
  }

  // Desktop/TV Layout with side menu
  Widget _buildDesktopTVLayout() {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Focus(
        onKeyEvent: (node, event) {
          if (event is KeyDownEvent) {
            if (event.logicalKey == LogicalKeyboardKey.escape) {
              setState(() {
                _isMenuVisible = !_isMenuVisible;
              });
              return KeyEventResult.handled;
            }
          }
          return KeyEventResult.ignored;
        },
        child: Row(
          children: [
            // Side Menu
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              width: _isMenuVisible ? 350 : 0,
              child:
                  _isMenuVisible
                      ? Focus(
                        focusNode: _menuFocusNode,
                        child: const TVSideMenu(),
                      )
                      : null,
            ),

            // Main Content
            Expanded(
              child: Focus(
                focusNode: _contentFocusNode,
                child: _buildMainContent(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Mobile/Tablet Layout with drawer
  Widget _buildMobileTabletLayout() {
    return Scaffold(
      appBar: AppBar(
        title:
            _isSearching
                ? TextField(
                  controller: _searchController,
                  autofocus: true,
                  style: const TextStyle(color: Colors.white),
                  decoration: const InputDecoration(
                    hintText: 'البحث عن قناة...',
                    hintStyle: TextStyle(color: Colors.white70),
                    border: InputBorder.none,
                  ),
                  onChanged: (value) {
                    Provider.of<ChannelsProvider>(
                      context,
                      listen: false,
                    ).searchChannels(value);
                  },
                )
                : const Text('Hunter TV'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: Icon(_isSearching ? Icons.close : Icons.search),
            onPressed: () {
              setState(() {
                _isSearching = !_isSearching;
                if (!_isSearching) {
                  _searchController.clear();
                  Provider.of<ChannelsProvider>(
                    context,
                    listen: false,
                  ).searchChannels('');
                }
              });
            },
          ),
          Consumer<ChannelsProvider>(
            builder: (context, provider, child) {
              return IconButton(
                icon: const Icon(Icons.refresh),
                onPressed:
                    provider.isLoading
                        ? null
                        : () async {
                          await provider.refreshChannels();
                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('تم تحديث القنوات بنجاح'),
                              ),
                            );
                          }
                        },
              );
            },
          ),
        ],
      ),
      drawer: const SideMenu(),
      body: _buildMainContent(),
    );
  }

  Widget _buildMainContent() {
    return Consumer<ChannelsProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(
                  color: _isDesktop(context) ? Colors.white : null,
                  strokeWidth: 3,
                ),
                const SizedBox(height: 24),
                Text(
                  'جاري تحميل القنوات...',
                  style: TextStyle(
                    color: _isDesktop(context) ? Colors.white : null,
                    fontSize: 18,
                  ),
                ),
              ],
            ),
          );
        }

        if (provider.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 80,
                  color: _isDesktop(context) ? Colors.red : Colors.red[400],
                ),
                const SizedBox(height: 24),
                Text(
                  'خطأ في تحميل القنوات',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: _isDesktop(context) ? Colors.white : null,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  provider.error!,
                  style: TextStyle(
                    fontSize: 16,
                    color: _isDesktop(context) ? Colors.grey : Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),
                ElevatedButton.icon(
                  onPressed: () => provider.refreshChannels(),
                  icon: const Icon(Icons.refresh),
                  label: const Text('إعادة المحاولة'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                ),
              ],
            ),
          );
        }

        // Determine which channels to show
        List<dynamic> channelsToShow;
        String title;

        if (provider.searchQuery.isNotEmpty) {
          channelsToShow = provider.searchResults;
          title = 'نتائج البحث (${provider.searchResults.length})';
        } else if (provider.selectedGroupName.isEmpty) {
          channelsToShow =
              provider.channelGroups.expand((group) => group.channels).toList();
          title = 'جميع القنوات (${provider.totalChannelsCount})';
        } else {
          channelsToShow = provider.selectedGroupChannels;
          title =
              '${provider.selectedGroupName} (${provider.selectedGroupChannels.length})';
        }

        if (channelsToShow.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.tv_off,
                  size: 80,
                  color: _isDesktop(context) ? Colors.grey : Colors.grey[400],
                ),
                const SizedBox(height: 24),
                Text(
                  provider.searchQuery.isNotEmpty
                      ? 'لا توجد نتائج للبحث'
                      : 'لا توجد قنوات متاحة',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: _isDesktop(context) ? Colors.white : null,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  _isDesktop(context)
                      ? 'اضغط على زر Escape لفتح القائمة'
                      : 'اضغط على زر القائمة للتنقل بين الأقسام',
                  style: TextStyle(
                    color: _isDesktop(context) ? Colors.grey : Colors.grey[600],
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          );
        }

        return Column(
          children: [
            // Header (only for desktop/TV)
            if (_isDesktop(context))
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[900],
                  border: Border(bottom: BorderSide(color: Colors.grey[800]!)),
                ),
                child: Row(
                  children: [
                    // Menu Toggle Button
                    IconButton(
                      onPressed: () {
                        setState(() {
                          _isMenuVisible = !_isMenuVisible;
                        });
                      },
                      icon: const Icon(Icons.menu, color: Colors.white),
                      tooltip: 'القائمة (Escape)',
                    ),
                    if (!_isMenuVisible) const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        title,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color: Theme.of(
                          context,
                        ).primaryColor.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.tv,
                            color: Theme.of(context).primaryColor,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Hunter TV',
                            style: TextStyle(
                              color: Theme.of(context).primaryColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

            // Channels Grid
            Expanded(
              child: Padding(
                padding: _getPadding(context),
                child: GridView.builder(
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: _getCrossAxisCount(context),
                    childAspectRatio: _getChildAspectRatio(context),
                    crossAxisSpacing: _getSpacing(context),
                    mainAxisSpacing: _getSpacing(context),
                  ),
                  itemCount: channelsToShow.length,
                  itemBuilder: (context, index) {
                    final channel = channelsToShow[index];

                    if (_isDesktop(context)) {
                      return TVGridCard(
                        channel: channel,
                        autofocus: index == 0,
                        onTap: () => provider.playChannel(channel, context),
                      );
                    } else {
                      return ResponsiveChannelCard(
                        channel: channel,
                        onTap: () => provider.playChannel(channel, context),
                        isMobile: _isMobile(context),
                      );
                    }
                  },
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
