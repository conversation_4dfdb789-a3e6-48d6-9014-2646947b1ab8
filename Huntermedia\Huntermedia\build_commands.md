# أوامر بناء APK محسنة للسرعة

## 🚀 بناء سريع للتطوير (أسرع):
```bash
# بناء debug APK (أسرع - للاختبار)
flutter build apk --debug

# بناء profile APK (متوسط السرعة - للاختبار مع أداء أفضل)
flutter build apk --profile
```

## 📦 بناء للإنتاج (محسن):
```bash
# بناء release APK مع تحسينات (الأفضل للنشر)
flutter build apk --release --shrink

# بناء APK مقسم حسب المعمارية (أصغر حجماً)
flutter build apk --release --split-per-abi

# بناء App Bundle للنشر في Google Play (الأفضل)
flutter build appbundle --release
```

## ⚡ بناء سريع مع تخطي الاختبارات:
```bash
# تخطي الاختبارات لتسريع البناء
flutter build apk --release --no-test-assets

# بناء مع تحسين الحجم
flutter build apk --release --obfuscate --split-debug-info=build/debug-info
```

## 🔧 تنظيف وإعادة البناء:
```bash
# تنظيف البناء السابق
flutter clean

# إعادة تحميل المكتبات
flutter pub get

# بناء جديد
flutter build apk --release
```

## 📊 مقارنة أوقات البناء:

1. **Debug APK**: ~2-5 دقائق (أسرع)
2. **Profile APK**: ~3-7 دقائق (متوسط)
3. **Release APK**: ~5-15 دقيقة (أبطأ لكن محسن)
4. **Split APK**: ~7-20 دقيقة (أبطأ لكن أصغر حجماً)

## 💡 نصائح لتسريع البناء:

1. **استخدم Debug للاختبار السريع**
2. **استخدم Profile لاختبار الأداء**
3. **استخدم Release للنشر النهائي**
4. **تأكد من إغلاق البرامج الأخرى أثناء البناء**
5. **استخدم SSD بدلاً من HDD**
6. **تأكد من وجود ذاكرة كافية (8GB+)**
