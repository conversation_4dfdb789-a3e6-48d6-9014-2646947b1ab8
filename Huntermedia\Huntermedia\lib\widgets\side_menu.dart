import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/channels_provider.dart';
import '../models/vod_content.dart';
import '../widgets/enhanced_vod_section.dart';

class SideMenu extends StatelessWidget {
  const SideMenu({super.key});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Column(
        children: [
          // Header
          Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Theme.of(context).primaryColor,
                  Theme.of(context).primaryColor.withValues(alpha: 0.8),
                ],
              ),
            ),
            child: const SafeArea(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.tv, size: 60, color: Colors.white),
                  SizedBox(height: 16),
                  Text(
                    'Hunter TV',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'القنوات التلفزيونية',
                    style: TextStyle(color: Colors.white70, fontSize: 16),
                  ),
                ],
              ),
            ),
          ),

          // Menu Items
          Expanded(
            child: Consumer<ChannelsProvider>(
              builder: (context, provider, child) {
                if (provider.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (provider.channelGroups.isEmpty) {
                  return const Center(child: Text('لا توجد قنوات متاحة'));
                }

                return ListView(
                  padding: EdgeInsets.zero,
                  children: [
                    // Search option
                    ListTile(
                      leading: const Icon(Icons.search),
                      title: const Text('البحث'),
                      onTap: () {
                        Navigator.pop(context);
                        _showSearchDialog(context);
                      },
                    ),

                    const Divider(),

                    // VOD Options
                    ListTile(
                      leading: const Icon(Icons.movie),
                      title: const Text('أفلام'),
                      onTap: () {
                        Navigator.pop(context);
                        _showVodSection(context, VodType.movie);
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.tv),
                      title: const Text('مسلسلات'),
                      onTap: () {
                        Navigator.pop(context);
                        _showVodSection(context, VodType.series);
                      },
                    ),

                    const Divider(),

                    // All channels
                    ListTile(
                      leading: const Icon(Icons.all_inclusive),
                      title: Text(
                        'جميع القنوات (${provider.totalChannelsCount})',
                      ),
                      selected: provider.selectedGroupName.isEmpty,
                      onTap: () {
                        provider.selectGroup('');
                        Navigator.pop(context);
                      },
                    ),

                    const Divider(),

                    // Channel groups
                    ...provider.groupNames.map((groupName) {
                      final group = provider.channelGroups.firstWhere(
                        (g) => g.name == groupName,
                      );

                      return ListTile(
                        leading: _getGroupIcon(groupName),
                        title: Text('$groupName (${group.channels.length})'),
                        selected: provider.selectedGroupName == groupName,
                        onTap: () {
                          provider.selectGroup(groupName);
                          Navigator.pop(context);
                        },
                      );
                    }),
                  ],
                );
              },
            ),
          ),

          // Footer
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                const Divider(),
                Consumer<ChannelsProvider>(
                  builder: (context, provider, child) {
                    return Column(
                      children: [
                        ListTile(
                          leading: const Icon(Icons.settings),
                          title: const Text('إعدادات المشغل'),
                          onTap: () {
                            Navigator.pop(context);
                            _showPlayerSettings(context, provider);
                          },
                        ),
                        ListTile(
                          leading: const Icon(Icons.refresh),
                          title: const Text('تحديث القنوات'),
                          onTap: () async {
                            Navigator.pop(context);
                            await provider.refreshChannels();
                            if (context.mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('تم تحديث القنوات بنجاح'),
                                ),
                              );
                            }
                          },
                        ),
                      ],
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Icon _getGroupIcon(String groupName) {
    final lowerName = groupName.toLowerCase();

    if (lowerName.contains('أفلام') || lowerName.contains('movies')) {
      return const Icon(Icons.movie);
    } else if (lowerName.contains('رياضة') || lowerName.contains('sport')) {
      return const Icon(Icons.sports_soccer);
    } else if (lowerName.contains('أطفال') || lowerName.contains('kids')) {
      return const Icon(Icons.child_care);
    } else if (lowerName.contains('أخبار') || lowerName.contains('news')) {
      return const Icon(Icons.newspaper);
    } else if (lowerName.contains('دينية') || lowerName.contains('religious')) {
      return const Icon(Icons.mosque);
    } else if (lowerName.contains('موسيقى') || lowerName.contains('music')) {
      return const Icon(Icons.music_note);
    } else if (lowerName.contains('وثائقي') ||
        lowerName.contains('documentary')) {
      return const Icon(Icons.description);
    } else {
      return const Icon(Icons.tv);
    }
  }

  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('البحث في القنوات'),
            content: TextField(
              autofocus: true,
              decoration: const InputDecoration(
                hintText: 'اكتب اسم القناة...',
                prefixIcon: Icon(Icons.search),
              ),
              onChanged: (query) {
                Provider.of<ChannelsProvider>(
                  context,
                  listen: false,
                ).searchChannels(query);
              },
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إغلاق'),
              ),
            ],
          ),
    );
  }

  void _showPlayerSettings(BuildContext context, ChannelsProvider provider) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('إعدادات المشغل'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('اختر نوع المشغل:'),
                const SizedBox(height: 16),
                RadioListTile<PlayerType>(
                  title: const Text('مشغل داخلي'),
                  subtitle: const Text('يدعم HLS, M3U8, MP4 وصيغ أخرى'),
                  value: PlayerType.internal,
                  groupValue: provider.preferredPlayer,
                  onChanged: (value) {
                    if (value != null) {
                      provider.setPreferredPlayer(value);
                    }
                  },
                ),
                RadioListTile<PlayerType>(
                  title: const Text('مشغل خارجي'),
                  subtitle: const Text('VLC, MX Player وتطبيقات أخرى'),
                  value: PlayerType.external,
                  groupValue: provider.preferredPlayer,
                  onChanged: (value) {
                    if (value != null) {
                      provider.setPreferredPlayer(value);
                    }
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إغلاق'),
              ),
            ],
          ),
    );
  }

  void _showVodSection(BuildContext context, VodType type) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => Scaffold(
              appBar: AppBar(
                title: Text(type == VodType.movie ? 'أفلام' : 'مسلسلات'),
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
              ),
              body: EnhancedVodSection(initialType: type),
            ),
      ),
    );
  }
}
