import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/vod_content.dart';

class VodService {
  static const String vodM3uUrl = 'https://raw.githubusercontent.com/hunter4ever007/huntertest/refs/heads/master/m3u/123.m3u';

  static Future<List<VodGroup>> fetchVodContent() async {
    try {
      final response = await http.get(Uri.parse(vodM3uUrl));
      
      if (response.statusCode == 200) {
        // Decode the response with UTF-8 encoding
        final content = utf8.decode(response.bodyBytes);
        return parseVodM3U(content);
      } else {
        throw Exception('Failed to load VOD M3U file: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching VOD M3U file: $e');
    }
  }

  static List<VodGroup> parseVodM3U(String content) {
    final lines = content.split('\n');
    final Map<String, List<VodContent>> groupedContent = {};
    
    for (int i = 0; i < lines.length; i++) {
      final line = lines[i].trim();
      
      // Check if this is an EXTINF line
      if (line.startsWith('#EXTINF:')) {
        // Get the next line which should be the URL
        if (i + 1 < lines.length) {
          final urlLine = lines[i + 1].trim();
          
          // Skip if URL line is empty or starts with #
          if (urlLine.isNotEmpty && !urlLine.startsWith('#')) {
            try {
              final vodContent = VodContent.fromM3ULine(line, urlLine);
              final groupName = _formatGroupName(vodContent.groupTitle ?? 'أفلام');
              
              if (!groupedContent.containsKey(groupName)) {
                groupedContent[groupName] = [];
              }
              groupedContent[groupName]!.add(vodContent);
            } catch (e) {
              // Skip malformed entries
              print('Error parsing VOD content: $e');
            }
          }
        }
      }
    }

    // Convert to VodGroup list and sort
    final groups = groupedContent.entries
        .map((entry) {
          // Determine group type based on content
          VodType groupType = VodType.movie;
          if (entry.value.isNotEmpty) {
            // Check if majority of content in this group is series
            final seriesCount = entry.value.where((item) => item.type == VodType.series).length;
            if (seriesCount > entry.value.length / 2) {
              groupType = VodType.series;
            }
          }
          
          return VodGroup(
            name: entry.key,
            content: entry.value,
            type: groupType,
          );
        })
        .toList();

    // Sort groups: Movies first, then series, then alphabetically
    groups.sort((a, b) {
      // Prioritize movie groups
      if (a.type == VodType.movie && b.type == VodType.series) return -1;
      if (a.type == VodType.series && b.type == VodType.movie) return 1;
      
      // Then sort alphabetically
      return a.name.compareTo(b.name);
    });

    return groups;
  }

  static String _formatGroupName(String groupName) {
    // Clean up group names and translate common terms
    String cleaned = groupName.trim();
    
    // Translation map for common terms
    final translations = {
      'Movies': 'أفلام',
      'TV Series': 'مسلسلات',
      'Series': 'مسلسلات',
      'Shows': 'برامج',
      'Documentary': 'وثائقي',
      'Animation': 'رسوم متحركة',
      'Kids': 'أطفال',
      'Action': 'أكشن',
      'Comedy': 'كوميديا',
      'Drama': 'دراما',
      'Horror': 'رعب',
      'Thriller': 'إثارة',
      'Romance': 'رومانسي',
      'Sci-Fi': 'خيال علمي',
      'Fantasy': 'فانتازيا',
      'War': 'حرب',
    };
    
    // Apply translations
    for (final entry in translations.entries) {
      if (cleaned.toLowerCase().contains(entry.key.toLowerCase())) {
        cleaned = cleaned.replaceAll(RegExp(entry.key, caseSensitive: false), entry.value);
      }
    }
    
    return cleaned.isEmpty ? 'أفلام' : cleaned;
  }

  // Helper method to get all movies
  static List<VodContent> getMoviesFromGroups(List<VodGroup> groups) {
    return groups
        .where((group) => group.type == VodType.movie)
        .expand((group) => group.content)
        .where((content) => content.type == VodType.movie)
        .toList();
  }

  // Helper method to get all series
  static List<VodContent> getSeriesFromGroups(List<VodGroup> groups) {
    return groups
        .where((group) => group.type == VodType.series)
        .expand((group) => group.content)
        .where((content) => content.type == VodType.series)
        .toList();
  }

  // Helper method to search content
  static List<VodContent> searchContent(List<VodGroup> groups, String query) {
    if (query.isEmpty) return [];
    
    final lowerQuery = query.toLowerCase();
    return groups
        .expand((group) => group.content)
        .where((content) =>
            content.name.toLowerCase().contains(lowerQuery) ||
            content.cleanTitle.toLowerCase().contains(lowerQuery) ||
            (content.groupTitle?.toLowerCase().contains(lowerQuery) ?? false))
        .toList();
  }
}
