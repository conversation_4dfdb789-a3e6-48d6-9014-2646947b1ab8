import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../models/channel.dart';

class ResponsiveChannelCard extends StatefulWidget {
  final Channel channel;
  final VoidCallback onTap;
  final bool isMobile;

  const ResponsiveChannelCard({
    super.key,
    required this.channel,
    required this.onTap,
    required this.isMobile,
  });

  @override
  State<ResponsiveChannelCard> createState() => _ResponsiveChannelCardState();
}

class _ResponsiveChannelCardState extends State<ResponsiveChannelCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    setState(() {
      _isPressed = true;
    });
    _animationController.forward();
  }

  void _onTapUp(TapUpDetails details) {
    setState(() {
      _isPressed = false;
    });
    _animationController.reverse();
    widget.onTap();
  }

  void _onTapCancel() {
    setState(() {
      _isPressed = false;
    });
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Card(
            elevation: _isPressed ? 8 : 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(widget.isMobile ? 12 : 16),
            ),
            child: GestureDetector(
              onTapDown: _onTapDown,
              onTapUp: _onTapUp,
              onTapCancel: _onTapCancel,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(
                    widget.isMobile ? 12 : 16,
                  ),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [Colors.white, Colors.grey[50]!],
                  ),
                ),
                child: Column(
                  children: [
                    // Channel Logo
                    Expanded(
                      flex: 3,
                      child: Container(
                        width: double.infinity,
                        padding: EdgeInsets.all(widget.isMobile ? 8 : 12),
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(
                              widget.isMobile ? 8 : 12,
                            ),
                            color: Colors.grey[100],
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.1),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(
                              widget.isMobile ? 8 : 12,
                            ),
                            child:
                                widget.channel.logo != null &&
                                        widget.channel.logo!.isNotEmpty
                                    ? CachedNetworkImage(
                                      imageUrl: widget.channel.logo!,
                                      fit: BoxFit.contain,
                                      placeholder:
                                          (context, url) => Container(
                                            color: Colors.grey[200],
                                            child: Center(
                                              child: CircularProgressIndicator(
                                                strokeWidth: 2,
                                                color:
                                                    Theme.of(
                                                      context,
                                                    ).primaryColor,
                                              ),
                                            ),
                                          ),
                                      errorWidget:
                                          (context, url, error) => Container(
                                            color: Colors.grey[200],
                                            child: Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                Icon(
                                                  Icons.tv,
                                                  color: Colors.grey[400],
                                                  size:
                                                      widget.isMobile ? 24 : 32,
                                                ),
                                                const SizedBox(height: 4),
                                                Text(
                                                  'قناة',
                                                  style: TextStyle(
                                                    color: Colors.grey[500],
                                                    fontSize:
                                                        widget.isMobile
                                                            ? 10
                                                            : 12,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                    )
                                    : Container(
                                      color: Colors.grey[200],
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Icon(
                                            Icons.tv,
                                            color: Colors.grey[400],
                                            size: widget.isMobile ? 24 : 32,
                                          ),
                                          const SizedBox(height: 4),
                                          Text(
                                            'قناة',
                                            style: TextStyle(
                                              color: Colors.grey[500],
                                              fontSize:
                                                  widget.isMobile ? 10 : 12,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                          ),
                        ),
                      ),
                    ),

                    // Channel Info
                    Expanded(
                      flex: 2,
                      child: Container(
                        width: double.infinity,
                        padding: EdgeInsets.all(widget.isMobile ? 8 : 12),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(
                              widget.isMobile ? 12 : 16,
                            ),
                            bottomRight: Radius.circular(
                              widget.isMobile ? 12 : 16,
                            ),
                          ),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // Channel Name
                            Flexible(
                              child: Text(
                                widget.channel.name,
                                style: TextStyle(
                                  fontSize: widget.isMobile ? 12 : 14,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.grey[800],
                                ),
                                textAlign: TextAlign.center,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),

                            // Channel Category (if available)
                            if (widget.channel.groupTitle != null &&
                                widget.channel.groupTitle!.isNotEmpty) ...[
                              const SizedBox(height: 4),
                              Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: widget.isMobile ? 6 : 8,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: Theme.of(
                                    context,
                                  ).primaryColor.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(
                                    widget.isMobile ? 8 : 10,
                                  ),
                                ),
                                child: Text(
                                  widget.channel.groupTitle!,
                                  style: TextStyle(
                                    fontSize: widget.isMobile ? 9 : 10,
                                    color: Theme.of(context).primaryColor,
                                    fontWeight: FontWeight.w500,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),

                    // Play Button
                    Container(
                      width: double.infinity,
                      height: widget.isMobile ? 32 : 40,
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor,
                        borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(
                            widget.isMobile ? 12 : 16,
                          ),
                          bottomRight: Radius.circular(
                            widget.isMobile ? 12 : 16,
                          ),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.play_arrow,
                            color: Colors.white,
                            size: widget.isMobile ? 16 : 20,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'تشغيل',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: widget.isMobile ? 11 : 13,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
