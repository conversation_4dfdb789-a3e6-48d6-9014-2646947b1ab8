class Channel {
  final String name;
  final String url;
  final String? logo;
  final String? groupTitle;
  final String? tvgId;
  final String? tvgName;

  Channel({
    required this.name,
    required this.url,
    this.logo,
    this.groupTitle,
    this.tvgId,
    this.tvgName,
  });

  factory Channel.fromM3ULine(String extinfLine, String urlLine) {
    // Parse EXTINF line
    // Example: #EXTINF:-1 tvg-id="channel1" tvg-name="Channel Name" tvg-logo="logo.png" group-title="Movies",Channel Display Name
    
    String name = '';
    String? logo;
    String? groupTitle;
    String? tvgId;
    String? tvgName;

    // Extract channel name (after the last comma)
    final commaIndex = extinfLine.lastIndexOf(',');
    if (commaIndex != -1 && commaIndex < extinfLine.length - 1) {
      name = extinfLine.substring(commaIndex + 1).trim();
    }

    // Extract attributes using regex
    final logoMatch = RegExp(r'tvg-logo="([^"]*)"').firstMatch(extinfLine);
    if (logoMatch != null) {
      logo = logoMatch.group(1);
    }

    final groupMatch = RegExp(r'group-title="([^"]*)"').firstMatch(extinfLine);
    if (groupMatch != null) {
      groupTitle = groupMatch.group(1);
    }

    final tvgIdMatch = RegExp(r'tvg-id="([^"]*)"').firstMatch(extinfLine);
    if (tvgIdMatch != null) {
      tvgId = tvgIdMatch.group(1);
    }

    final tvgNameMatch = RegExp(r'tvg-name="([^"]*)"').firstMatch(extinfLine);
    if (tvgNameMatch != null) {
      tvgName = tvgNameMatch.group(1);
    }

    return Channel(
      name: name,
      url: urlLine.trim(),
      logo: logo,
      groupTitle: groupTitle ?? 'عام',
      tvgId: tvgId,
      tvgName: tvgName,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'url': url,
      'logo': logo,
      'groupTitle': groupTitle,
      'tvgId': tvgId,
      'tvgName': tvgName,
    };
  }

  factory Channel.fromJson(Map<String, dynamic> json) {
    return Channel(
      name: json['name'] ?? '',
      url: json['url'] ?? '',
      logo: json['logo'],
      groupTitle: json['groupTitle'],
      tvgId: json['tvgId'],
      tvgName: json['tvgName'],
    );
  }
}
